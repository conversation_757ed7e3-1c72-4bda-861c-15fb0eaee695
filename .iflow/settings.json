{"mcpServers": {"fvt": {"httpUrl": "http://pre-xmca-cloudbot.aliyun-inc.com/fvt/mcp/", "headers": {"Content-Type": "application/json", "Authorization": "Bearer M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU="}}, "basic_info": {"httpUrl": "http://xmca-cloudbot.aliyun-inc.com/basic_info/mcp/", "headers": {"Content-Type": "application/json", "Authorization": "Bearer M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU="}}, "nc_down_prod": {"httpUrl": "http://xmca-cloudbot.aliyun-inc.com/nc_down/mcp/", "headers": {"Content-Type": "application/json", "Authorization": "Bearer M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU="}}, "nc_down_pre": {"httpUrl": "http://pre-xmca-cloudbot.aliyun-inc.com/nc_down/mcp/", "headers": {"Content-Type": "application/json", "Authorization": "Bearer M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU="}}, "operation": {"httpUrl": "http://xmca-cloudbot.aliyun-inc.com/operation/mcp/", "headers": {"Content-Type": "application/json", "Authorization": "Bearer M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU="}}, "diagnose": {"httpUrl": "http://xmca-cloudbot.aliyun-inc.com/diagnose/mcp/", "headers": {"Content-Type": "application/json", "Authorization": "Bearer M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU="}}, "vm_coredump": {"httpUrl": "http://ecs-mcp.alibaba-inc.com/vm_coredump/mcp/", "headers": {"Authorization": "token ************************************************"}}, "vm_migration": {"httpUrl": "https://ecs-mcp.alibaba-inc.com/vm_migration/mcp/", "headers": {"Authorization": "token ****************************************************"}}}}