"""
LLM模块单元测试
测试LLM实例创建、缓存和基本功能
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../src'))

import pytest
from typing import Optional
from langchain_openai import ChatOpenAI
from langchain_core.runnables.fallbacks import RunnableWithFallbacks

from deep_diagnose.llms.llm import get_llm_by_type, _llm_cache, clear_llm_cache
from deep_diagnose.common.config import get_config
from deep_diagnose.llms.agent_llm_config import LLMType


class TestLLMModule:
    """LLM模块测试类"""

    def test_llm_instance_creation(self) -> None:
        """测试LLM实例创建"""
        print("\n=== 测试LLM实例创建 ===")
        
        # 测试基础LLM创建
        basic_llm = get_llm_by_type("basic")
        assert basic_llm is not None
        assert isinstance(basic_llm, ChatOpenAI)
        print(f"✓ 基础LLM创建成功: {type(basic_llm)}")
        
        # 测试推理LLM创建
        reasoning_llm = get_llm_by_type("reasoning")
        assert reasoning_llm is not None
        # reasoning 允许返回RunnableWithFallbacks或ChatOpenAI（兼容）
        assert isinstance(reasoning_llm, (ChatOpenAI, RunnableWithFallbacks))
        print(f"✓ 推理LLM创建成功: {type(reasoning_llm)}")
        
        # 测试代码LLM创建
        code_llm = get_llm_by_type("code")
        assert code_llm is not None
        # code 也可能启用fallback，允许返回 RunnableWithFallbacks
        assert isinstance(code_llm, (ChatOpenAI, RunnableWithFallbacks))
        print(f"✓ 代码LLM创建成功: {type(code_llm)}")

    def test_llm_caching(self) -> None:
        """测试LLM实例缓存机制"""
        print("\n=== 测试LLM缓存机制 ===")
        
        # 清空缓存
        clear_llm_cache()
        
        # 第一次获取
        llm1 = get_llm_by_type("basic")
        assert "basic" in _llm_cache
        print(f"✓ 首次获取LLM，缓存已建立")
        
        # 第二次获取（应该从缓存中获取）
        llm2 = get_llm_by_type("basic")
        assert llm1 is llm2  # 应该是同一个实例
        print(f"✓ 第二次获取LLM，使用缓存实例")
        
        # 验证缓存大小
        cache_size = len(_llm_cache)
        print(f"✓ 当前缓存大小: {cache_size}")

    def test_llm_config_loading(self) -> None:
        """测试LLM配置加载"""
        print("\n=== 测试LLM配置加载 ===")
        
        try:
            config = get_config()
            assert config is not None
            print(f"✓ 配置加载成功")
            
            # 测试配置中是否包含LLM profiles
            assert hasattr(config.llm, 'profiles')
            assert hasattr(config.llm.profiles, 'basic')
            assert hasattr(config.llm.profiles, 'reasoning')
            print(f"✓ LLM配置profiles验证成功")
            
        except Exception as e:
            pytest.fail(f"配置加载失败: {e}")

    def test_invalid_llm_type(self) -> None:
        """测试无效的LLM类型处理"""
        print("\n=== 测试无效LLM类型处理 ===")
        
        with pytest.raises(ValueError, match="Unknown LLM type"):
            get_llm_by_type("invalid_type")
        
        print(f"✓ 无效LLM类型正确抛出异常")

    def test_llm_basic_functionality(self) -> None:
        """测试LLM基本功能"""
        print("\n=== 测试LLM基本功能 ===")
        
        try:
            # 获取基础LLM
            basic_llm = get_llm_by_type("basic")
            
            # 测试简单调用
            response = basic_llm.invoke("Hello")
            assert response is not None
            assert hasattr(response, 'content')
            print(f"✓ LLM基本调用成功")
            print(f"  - 响应类型: {type(response)}")
            print(f"  - 响应内容预览: {response.content[:100]}...")
            
        except Exception as e:
            print(f"⚠️ LLM调用测试跳过（可能由于网络或配置问题）: {e}")

    def test_all_llm_types(self) -> None:
        """测试所有支持的LLM类型"""
        print("\n=== 测试所有LLM类型 ===")
        
        # 定义支持的LLM类型
        supported_types: list[LLMType] = [
            "basic",
            "reasoning", 
            "vision",
            "code_flash",
            "glm",
            "code"
        ]
        
        for llm_type in supported_types:
            try:
                llm = get_llm_by_type(llm_type)
                assert llm is not None
                print(f"✓ {llm_type} LLM创建成功")
            except Exception as e:
                print(f"⚠️ {llm_type} LLM创建失败: {e}")

    def test_llm_configuration_validation(self) -> None:
        """测试LLM配置验证"""
        print("\n=== 测试LLM配置验证 ===")
        
        try:
            config = get_config()
            
            # 验证每种LLM类型的配置
            basic_conf = config.llm.profiles.basic
            assert isinstance(basic_conf, dict)
            print(f"✓ basic配置验证成功: {len(basic_conf)} 个参数")
            
            reasoning_conf = config.llm.profiles.reasoning
            assert isinstance(reasoning_conf, dict)
            print(f"✓ reasoning配置验证成功: {len(reasoning_conf)} 个参数")
            
            # 检查必要的配置参数
            for conf_name, conf in [("basic", basic_conf), ("reasoning", reasoning_conf)]:
                if 'model_name' in conf:
                    print(f"  - {conf_name} 模型: {conf.get('model_name')}")
                if 'temperature' in conf:
                    print(f"  - {conf_name} 温度: {conf.get('temperature')}")
            
        except Exception as e:
            pytest.fail(f"配置验证失败: {e}")


if __name__ == "__main__":
    """直接运行测试"""
    print("🧪 LLM模块测试开始\n")
    
    # 创建测试实例
    test_instance = TestLLMModule()
    
    # 1. 实例创建测试
    print("1. LLM实例创建测试")
    test_instance.test_llm_instance_creation()
    
    # 2. 缓存机制测试  
    print("\n2. LLM缓存机制测试")
    test_instance.test_llm_caching()
    
    # 3. 配置加载测试
    print("\n3. LLM配置加载测试")
    test_instance.test_llm_config_loading()
    
    # 4. 无效类型测试
    print("\n4. 无效LLM类型测试")
    test_instance.test_invalid_llm_type()
    
    # 5. 基本功能测试
    print("\n5. LLM基本功能测试")
    test_instance.test_llm_basic_functionality()
    
    # 6. 所有类型测试
    print("\n6. 所有LLM类型测试")
    test_instance.test_all_llm_types()
    
    # 7. 配置验证测试
    print("\n7. LLM配置验证测试")
    test_instance.test_llm_configuration_validation()
    
    print(f"\n🎉 LLM模块测试完成！")
    print(f"\n=== LLM模块特点 ===")
    print(f"✅ 支持多种LLM类型（basic, reasoning, vision, code等）")
    print(f"✅ 实例缓存机制，提高性能")
    print(f"✅ 统一的配置管理")
    print(f"✅ 类型安全和错误处理")