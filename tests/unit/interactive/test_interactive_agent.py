import asyncio
import unittest
from unittest.mock import AsyncMock, Mock, patch

from deep_diagnose.common.utils.string_utils import substring_after
from deep_diagnose.core.interactive.interactive_agent import InteractiveAgent
from deep_diagnose.core.interactive.state import ExecutionState


class TestInteractiveAgent(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        self.agent = InteractiveAgent()
        self.state = ExecutionState()
        self.state.raw_question = "test raw question"
        self.state.question = "test question"

    async def test_astream_with_main_flow(self):
        """测试astream方法的正常流程"""
        # 准备mock对象.
        mock_state = Mock()
        mock_state.is_finished = Mock(side_effect=[False, False, True])
        mock_state.to_display_str = Mock(side_effect=["1", "2", "3"])

        # 直接mock _main_flow 和 _test_flow 方法
        self.agent._create_state = AsyncMock(return_value=mock_state)
        self.agent._main_flow = AsyncMock()
        self.agent._test_flow = AsyncMock()

        # 执行测试
        question = "test question"
        messages = [{"role": "user", "content": "hello"}]
        kwargs = {"mode": None}
        result_events = []
        async for event in self.agent.astream(question, messages, **kwargs):
            result_events.append(event)

        # 验证调用
        self.agent._create_state.assert_awaited_once_with(kwargs, question, messages)
        self.agent._main_flow.assert_called_once_with(mock_state)
        self.agent._test_flow.assert_not_called()
        self.assertEqual(3, len(result_events))
        self.assertEqual("1", result_events[0].result)
        self.assertEqual(False, result_events[0].finished)
        self.assertEqual("2", result_events[1].result)
        self.assertEqual(False, result_events[1].finished)
        self.assertEqual("3", result_events[2].result)
        self.assertEqual(True, result_events[2].finished)

    @patch('asyncio.wait')
    async def test_execute_paths_with_both_tasks_fast_path_match(self, mock_wait):
        """测试同时有slow_path_task和fast_path_task的情况，且快速路径匹配"""
        # 创建模拟任务（使用Future模拟实际Task行为）
        slow_task = asyncio.Future()
        fast_task = asyncio.Future()
        fast_task.set_result("fast_path_result")

        # 设置mock asyncio.wait返回值。模拟fast_task先完成的情况
        mock_wait.return_value = ({fast_task}, {slow_task})

        # 设置state属性
        self.state.fast_path_match_completed = True
        self.state.fast_path_scenario = "TestScenario"

        # 执行方法
        await self.agent._execute_paths(self.state, slow_task, fast_task)

        # 验证只有fast_task完成，slow_task没有完成
        self.assertTrue(fast_task.done())
        self.assertFalse(slow_task.done())

        # 验证asyncio.wait被正确调用
        mock_wait.assert_called_once_with([slow_task, fast_task], return_when=asyncio.FIRST_COMPLETED)

    @patch('asyncio.wait')
    async def test_execute_paths_with_both_tasks_but_no_fast_path_match(self, mock_wait):
        """测试同时有slow_path_task和fast_path_task但未命中快速路径的情况"""
        # 创建模拟任务（使用Future模拟实际Task行为）
        slow_task = asyncio.Future()
        slow_task.set_result("slow_path_result")
        fast_task = asyncio.Future()
        fast_task.set_result("fast_path_result")

        # 设置mock asyncio.wait返回值。模拟fast_task先完成的情况
        mock_wait.return_value = ({fast_task}, {slow_task})

        # 设置state属性，未命中快速路径
        self.state.fast_path_match_completed = False
        self.state.fast_path_scenario = None

        # 执行方法
        await self.agent._execute_paths(self.state, slow_task, fast_task)

        # 验证两个task都已完成
        self.assertTrue(slow_task.done())
        self.assertTrue(fast_task.done())

        # 验证asyncio.wait被调用
        mock_wait.assert_called_once_with([slow_task, fast_task], return_when=asyncio.FIRST_COMPLETED)

    @patch('asyncio.wait')
    async def test_execute_paths_with_slow_path_first_completion(self, mock_wait):
        """测试慢速路径先完成，快速路径后完成的情况"""
        # 创建模拟任务（使用Future模拟实际Task行为）
        slow_task = asyncio.Future()
        slow_task.set_result("slow_path_result")
        fast_task = asyncio.Future()

        # 设置mock asyncio.wait返回值。模拟fast_task先完成的情况
        mock_wait.return_value = ({fast_task}, {slow_task})

        # 设置state属性，未命中快速路径
        self.state.fast_path_match_completed = False
        self.state.fast_path_scenario = None

        # 执行方法
        await self.agent._execute_paths(self.state, slow_task, fast_task)

        # 验证两个task都已完成
        self.assertTrue(slow_task.done())
        self.assertFalse(fast_task.done())

        # 验证asyncio.wait被调用
        mock_wait.assert_called_once_with([slow_task, fast_task], return_when=asyncio.FIRST_COMPLETED)

    async def test_execute_paths_with_only_slow_task(self):
        """测试只有slow_path_task的情况"""
        # 创建模拟任务
        slow_task = asyncio.Future()
        slow_task.set_result("slow_path_result")

        # 执行方法
        await self.agent._execute_paths(self.state, slow_task, None)

        # 验证slow_task被调用
        self.assertTrue(slow_task.done())

    async def test_execute_paths_with_only_fast_task(self):
        """测试只有fast_path_task的情况"""
        # 创建模拟任务
        fast_task = asyncio.Future()
        fast_task.set_result("fast_path_result")

        # 执行方法
        await self.agent._execute_paths(self.state, None, fast_task)

        # 验证fast_task被调用
        self.assertTrue(fast_task.done())

    async def test_execute_paths_with_no_tasks(self):
        """测试没有任务的情况"""

        # 执行方法，应该不会抛出异常
        await self.agent._execute_paths(self.state, None, None)

    @patch('deep_diagnose.core.interactive.interactive_agent.planner_test')
    async def test_test_flow_with_planner_module(self, mock_planner_test):
        """测试_test_flow方法的planner模块路径"""
        # 准备mock对象
        mock_planner_test.return_value = "planner test result"

        # 执行测试
        await self.agent._test_flow(self.state, "planner")

        # 验证调用
        mock_planner_test.assert_awaited_once_with(self.state)
        self.assertTrue(self.state.fast_path_match_completed)
        self.assertTrue(self.state.conclusion_started)
        self.assertEqual(self.state.question, self.state.raw_question)
        self.assertEqual(self.state.result_content, "planner test result")
        self.assertTrue(self.state.finished)

    @patch('deep_diagnose.core.interactive.interactive_agent.param_extractor_test')
    async def test_test_flow_with_param_extractor_module(self, mock_param_extractor_test):
        """测试_test_flow方法的param_extractor模块路径"""
        # 准备mock对象
        mock_param_extractor_test.return_value = "param extractor test result"

        # 执行测试
        await self.agent._test_flow(self.state, "param_extractor")

        # 验证调用
        mock_param_extractor_test.assert_awaited_once_with(self.state)
        self.assertTrue(self.state.fast_path_match_completed)
        self.assertTrue(self.state.conclusion_started)
        self.assertEqual(self.state.question, self.state.raw_question)
        self.assertEqual(self.state.result_content, "param extractor test result")
        self.assertTrue(self.state.finished)

    @patch('deep_diagnose.core.interactive.interactive_agent.question_classifier_test')
    async def test_test_flow_with_question_classifier_module(self, mock_question_classifier_test):
        """测试_test_flow方法的question_classifier模块路径"""
        # 准备mock对象
        mock_question_classifier_test.return_value = "question classifier test result"

        # 执行测试
        await self.agent._test_flow(self.state, "question_classifier")

        # 验证调用
        mock_question_classifier_test.assert_awaited_once_with(self.state)
        self.assertTrue(self.state.fast_path_match_completed)
        self.assertTrue(self.state.conclusion_started)
        self.assertEqual(self.state.question, self.state.raw_question)
        self.assertEqual(self.state.result_content, "question classifier test result")
        self.assertTrue(self.state.finished)

    async def test_test_flow_with_unknown_module(self):
        """测试_test_flow方法的默认路径（未知模块）"""
        # 执行测试
        await self.agent._test_flow(self.state, "unknown_module")

        # 验证状态
        self.assertTrue(self.state.fast_path_match_completed)
        self.assertTrue(self.state.conclusion_started)
        self.assertEqual(self.state.question, self.state.raw_question)
        self.assertEqual(self.state.result_content, "")
        self.assertTrue(self.state.finished)

    @patch('deep_diagnose.core.interactive.interactive_agent.planner_test')
    async def test_test_flow_with_exception_handling(self, mock_planner_test):
        """测试_test_flow方法的异常处理"""
        # 准备mock对象
        mock_planner_test.side_effect = Exception("Test exception")

        # 执行测试
        await self.agent._test_flow(self.state, "planner")

        # 验证调用
        mock_planner_test.assert_awaited_once_with(self.state)
        self.assertTrue(self.state.fast_path_match_completed)
        self.assertTrue(self.state.conclusion_started)
        self.assertEqual(self.state.question, self.state.raw_question)
        self.assertTrue(self.state.finished)  # 即使出错也要设置finished为True


class FvtTestInteractiveAgent(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        self.agent = InteractiveAgent()

    async def test_module_test(self):
        question = "系统内的cpu 使用率， 和ecs 底层采集的cpu使用率不一致，为什么"
        last_value = None
        async for value in self.agent.astream(question, messages=None, authority="dev", mode=None,
                                              additional_info={"module": "question_classifier"}):
            last_value = value.result

        scenario = substring_after(last_value, "## 回复\n\n")
        self.assertEqual("KnowledgeQuery", scenario)

    async def test_main_flow(self):
        messages = [
            {'role': 'user', 'content': 'hi', "agent": "InteractiveAgent"},
            {'role': 'assistant', "agent": "InteractiveAgent",
             'content': '{"result": "## 回复\\n\\ni-wz9b873bysppltjo2qpb 所属客户的AliUid是 1871202333741276。"}'}
        ]

        question = "这个UID对应的客户名称是什么？"
        last_value = None
        async for value in self.agent.astream(question, messages=messages, authority="dev", mode=None):
            last_value = value.result

        response = substring_after(last_value, "## 回复\n\n")
        self.assertTrue("阿里巴巴云计算(北京)有限公司" in response)


if __name__ == '__main__':
    unittest.main()
