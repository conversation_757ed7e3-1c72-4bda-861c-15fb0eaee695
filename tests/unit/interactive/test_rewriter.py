import json
import unittest
from deep_diagnose.core.interactive.rewriter import _gen_history
from deep_diagnose.core.interactive.state import default_response


class TestGenHistory(unittest.TestCase):

    def test_gen_history_with_interactive_agent_messages(self):
        """测试处理InteractiveAgent类型的消息"""
        messages = [
            {
                "agent": "InteractiveAgent",
                "role": "user",
                "content": "我想诊断我的ECS实例"
            },
            {
                "agent": "InteractiveAgent",
                "role": "assistant",
                "content": '{"result": "<details>工具调用结果</details>## 回复\\n\\n这是诊断\\n结果"}'
            }
        ]

        result = _gen_history(messages)
        expected = "user: 我想诊断我的ECS实例\nassistant: 这是诊断结果"
        self.assertEqual(result, expected)

    def test_gen_history_with_inspect_agent_messages(self):
        """测试处理InspectAgent类型的消息"""
        messages = [
            {
                "agent": "InspectAgent",
                "role": "user",
                "content": "诊断实例",
                "ext": {
                    "machine_id": "i-1234567890abcdef0",
                    "start_time": "2023-01-01 10:00:00",
                    "end_time": "2023-01-01 11:00:00"
                }
            }
        ]

        result = _gen_history(messages)
        expected = 'user: [2023-01-01 10:00:00 - 2023-01-01 11:00:00] 对虚拟机i-1234567890abcdef0进行检查和诊断。'
        self.assertEqual(result, expected)

    def test_gen_history_with_default_response(self):
        """测试处理默认回复的情况"""
        messages = [
            {
                "agent": "InteractiveAgent",
                "role": "assistant",
                "content": json.dumps({"result": "## 回复\n\n" + default_response}, ensure_ascii=False)
            }
        ]

        result = _gen_history(messages)
        expected = "assistant: 问题无法识别"
        self.assertEqual(result, expected)

    def test_gen_history_ignore_other_agents(self):
        """测试忽略其他类型的agent消息"""
        messages = [
            {
                "agent": "OtherAgent",
                "role": "user",
                "content": "这条消息应该被忽略"
            },
            {
                "agent": "InteractiveAgent",
                "role": "user",
                "content": "这条消息应该被处理"
            }
        ]

        result = _gen_history(messages)
        expected = "user: 这条消息应该被处理"
        self.assertEqual(result, expected)

    def test_gen_history_with_nc_machine_type(self):
        """测试处理物理机类型的消息"""
        messages = [
            {
                "agent": "InspectAgent",
                "role": "user",
                "content": "诊断物理机",
                "ext": {
                    "machine_id": "10.25.36.32",
                    "start_time": "2023-01-01 10:00:00",
                    "end_time": "2023-01-01 11:00:00"
                }
            }
        ]

        result = _gen_history(messages)
        expected = 'user: [2023-01-01 10:00:00 - 2023-01-01 11:00:00] 对物理机10.25.36.32进行检查和诊断。'
        self.assertEqual(result, expected)


if __name__ == '__main__':
    unittest.main()
