import unittest
from unittest.mock import patch, MagicMock
import asyncio
from deep_diagnose.core.interactive.utils.dataset_collector import SLSDatasetCollector, _client


class TestSLSDatasetCollector(unittest.TestCase):
    def setUp(self):
        """在每个测试方法之前执行，创建SLSDatasetCollector实例"""
        self.collector = SLSDatasetCollector(None)

    def test_init(self):
        """测试初始化"""
        self.assertEqual(None, self.collector.client)
        self.assertEqual("ecs-predict-result", self.collector.project)
        self.assertEqual("ecs_deep_diagnose_interactive_dataset", self.collector.logstore)
        self.assertEqual([], self.collector.log_queue)

    def test_add_log_success(self):
        """测试成功添加日志记录"""
        record = {
            "event": "test_event",
            "data": {"key": "value"},
            "timestamp": 1234567890
        }

        # 执行添加日志操作
        self.collector.add_log(record)

        # 验证日志项已添加到队列
        self.assertEqual(1, len(self.collector.log_queue))
        log_item = self.collector.log_queue[0]

        # 验证日志项内容
        contents = log_item.get_contents()
        content_dict = dict(contents)

        self.assertEqual("test_event", content_dict["event"])
        self.assertEqual('{"key": "value"}', content_dict["data"])
        self.assertEqual("1234567890", content_dict["timestamp"])

    def test_add_log_with_various_data_types(self):
        """测试添加包含不同数据类型的日志记录"""
        record = {
            "string_value": "test",
            "int_value": 42,
            "float_value": 3.14,
            "bool_value": True,
            "list_value": [1, 2, 3],
            "dict_value": {"nested": "value"},
            "none_value": None,
            "set_value": {1, 2, 3},
        }

        self.collector.add_log(record)
        self.assertEqual(1, len(self.collector.log_queue))

        log_item = self.collector.log_queue[0]
        contents = log_item.get_contents()
        content_dict = dict(contents)

        self.assertEqual("test", content_dict["string_value"])
        self.assertEqual("42", content_dict["int_value"])
        self.assertEqual("3.14", content_dict["float_value"])
        self.assertEqual("True", content_dict["bool_value"])
        self.assertEqual("[1, 2, 3]", content_dict["list_value"])
        self.assertEqual('{"nested": "value"}', content_dict["dict_value"])
        self.assertEqual("None", content_dict["none_value"])
        self.assertEqual("{1, 2, 3}", content_dict["set_value"])


if __name__ == '__main__':
    unittest.main()
