<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RCA 报告: 最终报告内容</title>
    <!-- 浏览器标签页图标（favicon） -->
    <link rel="shortcut icon" href="https://cloudbot2.aliyun-inc.com/cloudbot/img/favicon_front.ico">
    <!-- 可选的兼容写法 -->
    <link rel="icon" type="image/x-icon" href="https://cloudbot2.aliyun-inc.com/cloudbot/img/favicon_front.ico">
    <!-- 仅依赖此CDN脚本 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 基础幻灯片样式 */
        html { scroll-behavior: smooth; }
        body { 
            overflow-x: hidden;
            margin: 0;
            padding: 0;
        }
        section { 
            min-height: 100vh; 
            width: 100%; 
            overflow-x: hidden;
            position: relative;
        }
        .mono { font-family: 'SF Mono', 'Courier New', Courier, monospace; }
        
        /* 防止内容溢出 */
        .section-content { 
            max-width: 100%; 
            overflow-wrap: break-word; 
            word-wrap: break-word;
        }
        
        /* 紧凑布局样式 */
        .compact-section {
            padding-top: 1rem !important;
            padding-bottom: 1rem !important;
        }
        .compact-spacing {
            margin-bottom: 0.5rem !important;
        }
        
        /* 统一宽度 */
        section {
            padding-left: 1.5rem !important;
            padding-right: 1.5rem !important;
        }
        
        @media (min-width: 768px) {
            section {
                padding-left: 5rem !important;
                padding-right: 5rem !important;
            }
        }
        
        /* 移除嵌套section的额外padding */
        section section {
            padding-top: 0 !important;
            padding-bottom: 0 !important;
            min-height: auto !important;
            padding-left: 0 !important;
            padding-right: 0 !important;
        }
        
        /* 减少间距 */
        .space-y-6 > * + * {
            margin-top: 1.5rem !important;
        }
        
        /* 进度条 */
        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #38b2ac, #4fd1c7);
            z-index: 1001;
            transition: width 0.3s ease;
        }
        
        /* 简化的控制栏 */
        .slide-controls {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            display: flex;
            gap: 16px;
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(15px);
            padding: 12px 20px;
            border-radius: 40px;
            border: 1px solid rgba(56, 178, 172, 0.2);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }
        
        .control-btn {
            background: rgba(56, 178, 172, 0.2);
            border: 1px solid rgba(56, 178, 172, 0.4);
            color: #38b2ac;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .control-btn:hover {
            background: rgba(56, 178, 172, 0.3);
            border-color: #38b2ac;
            color: #4fd1c7;
        }
        
        .control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .slide-indicator {
            background: transparent;
            border: none;
            color: #64748b;
            font-size: 14px;
            font-weight: 500;
            cursor: default;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        /* 点导航 */
        .slide-dots {
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            display: flex;
            gap: 6px;
        }
        
        .slide-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(100, 116, 139, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .slide-dot.active {
            background: #38b2ac;
            transform: scale(1.2);
        }
        
        .slide-dot:hover {
            background: #4fd1c7;
        }
        
        /* 键盘提示 */
        .keyboard-hint {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(30, 41, 59, 0.9);
            backdrop-filter: blur(10px);
            color: #94a3b8;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            border: 1px solid rgba(100, 116, 139, 0.3);
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }
        
        .keyboard-hint:hover {
            opacity: 1;
        }
        
        /* 隐藏所有幻灯片，只显示当前的 */
        .slide {
            display: none;
        }
        
        .slide.active {
            display: flex;
        }
    </style>
</head>
<body class="bg-slate-900 text-slate-300 font-sans antialiased">

    <!-- 进度条 -->
    <div class="progress-bar" id="progressBar"></div>

    <!-- 键盘导航提示 -->
    <div class="keyboard-hint">
        <span>← → 切换幻灯片</span>
    </div>

    <!-- 简化的幻灯片控制栏 -->
    <div class="slide-controls">
        <button class="control-btn" id="prevBtn">
            <span>◀</span> 上一页
        </button>
        <div class="slide-indicator">
            <span>第 <span id="currentSlide">1</span> 页 / 共 <span id="totalSlides">6</span> 页</span>
        </div>
        <button class="control-btn" id="nextBtn">
            下一页 <span>▶</span>
        </button>
    </div>

    <!-- 幻灯片点导航 -->
    <div class="slide-dots">
        
        <div class="slide-dot active" data-slide="0"></div>
        
        <div class="slide-dot" data-slide="1"></div>
        
        <div class="slide-dot" data-slide="2"></div>
        
        <div class="slide-dot" data-slide="3"></div>
        
        <div class="slide-dot" data-slide="4"></div>
        
        <div class="slide-dot" data-slide="5"></div>
        
    </div>


    <!-- Slide 1: 封面页 -->
    <section class="slide min-h-screen flex-col justify-center px-6 md:px-20 py-12 relative compact-section active" data-slide="0">
        
            <div class="space-y-4 flex-grow flex flex-col justify-center compact-spacing">
                <section class="min-h-screen flex flex-col items-center justify-center text-center p-8 bg-grid-slate-800/20 relative">
    <div class="absolute inset-0 bg-gradient-to-b from-slate-900 via-slate-900/80 to-slate-900"></div>
    <div class="relative z-10 animate-fade-in-up">
        <span class="text-sky-400 font-semibold">根本原因分析 (RCA) 报告</span>
        <h1 class="text-4xl md:text-6xl font-bold text-white mt-2">ECS 实例操作系统内核异常诊断</h1>
        <p class="mt-6 text-lg md:text-xl text-slate-400 max-w-4xl mx-auto">
            诊断实例 <code class="mono text-base md:text-lg text-white">i-2zej47d699e60ikj68vv</code> 在 2025-09-01 10:40:55 报告的操作系统崩溃问题。经分析，根本原因为虚拟机操作系统内核异常（vm_kernel:hang_task:fatal），发生于 2025-08-27 22:17:10。
        </p>
        <div class="mt-8 flex items-center justify-center space-x-6 text-slate-400">
            <span class="mono">用户ID: 1739641830090024</span>
            <span class="text-slate-600">|</span>
            <span>报告日期: 2025-09-16</span>
        </div>

    </div>
</section>
            </div>
        
    </section>

    <!-- Slide 2: 问题概述 -->
    <section class="slide min-h-screen flex-col justify-center px-6 md:px-20 py-12 relative compact-section" data-slide="1">
        
            <div class="space-y-4 flex-grow flex flex-col justify-center compact-spacing">
                
                    <section class="min-h-screen flex flex-col px-6 md:px-20 py-16 relative">
    <header class="pb-4 border-b border-slate-700 mb-6">
        <h2 class="text-3xl font-bold text-white">问题摘要</h2>
        <p class="text-slate-400">当前报告的核心问题陈述</p>
    </header>
    <div class="space-y-4 flex-grow">
        <!-- 核心信息 -->
        <div class="bg-slate-800 p-6 rounded-lg border border-slate-700">
            <h3 class="flex items-center text-xl font-semibold text-sky-400 mb-4">
                <svg class="w-6 h-6 mr-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                核心信息
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <p class="text-sm text-slate-400 mb-1">涉及资源</p>
                    <p class="font-semibold text-white mono">i-2zej47d699e60ikj68vv</p>
                </div>
                <div>
                    <p class="text-sm text-slate-400 mb-1">发生时间</p>
                    <p class="font-semibold text-white mono">2025-08-27 22:17:10</p>
                </div>
                <div>
                    <p class="text-sm text-slate-400 mb-1">问题类型</p>
                    <p class="font-semibold text-red-400">可用性问题</p>
                </div>
            </div>
        </div>
        <!-- 问题概述 -->
        <div class="bg-slate-800 p-6 rounded-lg border border-slate-700">
            <h3 class="flex items-center text-xl font-semibold text-sky-400 mb-4">
                 <svg class="w-6 h-6 mr-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" /></svg>
                问题概述
            </h3>
            <p class="text-slate-400 leading-relaxed">
                实例 <code class="mono text-sm">i-2zej47d699e60ikj68vv</code> 在 <span class="text-blue-300 font-mono">2025-08-27 22:17:10</span> 出现 <span class="text-red-400">虚拟机操作系统内核异常</span> (<code class="mono text-sm">vm_kernel:hang_task:fatal</code>)，导致系统夯死或蓝屏，可能引发实例不可用。
            </p>
        </div>
    </div>
</section>
                
            </div>
        
    </section>

    <!-- Slide 3: 诊断分析 -->
    <section class="slide min-h-screen flex-col justify-center px-6 md:px-20 py-12 relative compact-section" data-slide="2">
        
            <div class="space-y-4 flex-grow flex flex-col justify-center compact-spacing">
                
                    <section class="min-h-screen flex flex-col px-6 md:px-20 py-16 relative">
    <header class="pb-4 border-b border-slate-700 mb-6">
        <h2 class="text-3xl font-bold text-white">关键结论</h2>
        <p class="text-slate-400">针对用户疑问的直接解答</p>
    </header>
    <div class="space-y-4 flex-grow">
        <!-- 关键结论卡片组 -->
        <div class="bg-slate-800 p-6 rounded-lg border border-slate-700">
            <h3 class="flex items-center text-xl font-semibold text-sky-400 mb-4">
                <svg class="w-6 h-6 mr-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                关键技术结论
            </h3>
            <div class="space-y-6">
                <div class="bg-slate-700 p-4 rounded-lg border border-slate-600">
                    <h4 class="font-semibold text-white mb-2">1. 实例 <code class="mono">i-2zej47d699e60ikj68vv</code> 是否发生操作系统崩溃？</h4>
                    <p class="text-red-400"><strong>结论：发生操作系统内核异常。</strong> 在 <span class="text-blue-300 font-mono">2025-08-27 22:17:10</span> 发生 <code class="mono">vm_kernel:hang_task:fatal</code> 异常，表明虚拟机操作系统内核出现严重问题，可能导致系统夯死或蓝屏。</p>
                </div>
                <div class="bg-slate-700 p-4 rounded-lg border border-slate-600">
                    <h4 class="font-semibold text-white mb-2">2. 实例是否发生宕机或重启事件？</h4>
                    <p class="text-green-400"><strong>结论：未记录到宕机或重启事件。</strong> 在 <code class="mono text-sm">2025-08-27 00:00:00</code> 至 <code class="mono text-sm">2025-09-02 00:00:00</code> 时间范围内，未发现实例的宕机或重启事件记录，用户ID下的所有实例在此期间也运行稳定。</p>
                </div>
                <div class="bg-slate-700 p-4 rounded-lg border border-slate-600">
                    <h4 class="font-semibold text-white mb-2">3. 是否存在grub引导故障？</h4>
                    <p class="text-slate-400"><strong>结论：未发现grub引导故障证据。</strong> 诊断数据未显示与grub配置或引导过程相关的异常，问题根源指向操作系统内核异常。</p>
                </div>
                <div class="bg-slate-700 p-4 rounded-lg border border-slate-600">
                    <h4 class="font-semibold text-white mb-2">4. 实例是否发生迁移？是否与问题相关？</h4>
                    <p class="text-yellow-400"><strong>结论：发生一次冷迁移，但迁移本身成功。</strong> 实例在 <span class="text-blue-300 font-mono">2025-08-28 00:13</span> 发生冷迁移（原因：<code class="mono">rechedule_before_vm_start</code>，状态：<code class="mono">recover_success</code>）。内核异常发生在迁移前约1小时43分钟，迁移可能是对系统不稳定状态的响应。</p>
                </div>
                <div class="bg-slate-700 p-4 rounded-lg border border-slate-600">
                    <h4 class="font-semibold text-white mb-2">5. 物理机是否稳定？</h4>
                    <p class="text-green-400"><strong>结论：物理机运行稳定。</strong> 物理机NC（IP：<code class="mono">**************</code>）在相关时间窗口内无任何异常事件、运维操作或高危变更记录。</p>
                </div>
            </div>
        </div>
    </div>
</section>
                
            </div>
        
    </section>

    <!-- Slide 4: 关键发现 -->
    <section class="slide min-h-screen flex-col justify-center px-6 md:px-20 py-12 relative compact-section" data-slide="3">
        
            <div class="space-y-4 flex-grow flex flex-col justify-center compact-spacing">
                
                    <section class="min-h-screen flex flex-col justify-center px-6 md:px-20 py-16 relative">
    <header class="pb-4 border-b border-slate-700 mb-6">
        <h2 class="text-3xl font-bold text-white">关键发现</h2>
        <p class="text-slate-400">按层级结构的诊断发现分析</p>
    </header>
    <div class="space-y-6 flex-grow">
        <!-- ECS实例异常事件 -->
        <div class="bg-slate-800 p-6 rounded-lg border border-slate-700">
            <h3 class="text-xl font-semibold text-sky-400 mb-2">🔍 ECS实例异常事件</h3>
            <ul class="space-y-3 text-sm">
                <li class="pl-2 border-l-2 border-red-500">
                    <strong class="text-white">i-2zej47d699e60ikj68vv:</strong>
                    <span class="text-red-400">操作系统内核异常</span>导致系统夯死或蓝屏。
                    <ul class="list-disc list-inside mt-2 ml-2 space-y-1 text-slate-400">
                        <li><code class="mono text-xs"><span class="text-blue-300 font-medium">[22:17:10]</span></code>: 发生 <span class="bg-red-500 text-white px-1.5 py-0.5 rounded text-sm font-medium">vm_kernel:hang_task:fatal</span> 异常。</li>
                        <li>根本原因定位为 <span class="text-yellow-300 font-medium">虚拟机操作系统</span> 内部问题。</li>
                        <li>异常描述指出可能导致夯死或蓝屏，建议提交工单排查。</li>
                    </ul>
                </li>
                <li class="pl-2 border-l-2 border-yellow-500 mt-4">
                    <strong class="text-white">i-2zej47d699e60ikj68vv:</strong>
                    <span class="text-orange-400">冷迁移事件</span>发生在异常之后，可能加剧系统不稳定状态。
                    <ul class="list-disc list-inside mt-2 ml-2 space-y-1 text-slate-400">
                        <li><code class="mono text-xs"><span class="text-blue-300 font-medium">[00:13]</span></code>: 从源宿主机 <code class="mono text-xs">10.60.145.77</code> 迁移至目标宿主机 <code class="mono text-xs">10.250.205.214</code>。</li>
                        <li>迁移原因为 <span class="text-yellow-300 font-medium">启动前重调度（rechedule_before_vm_start）</span>。</li>
                        <li>迁移状态为 <span class="text-green-400">成功（recover_success）</span>，未直接引发崩溃。</li>
                    </ul>
                </li>
            </ul>
        </div>

        <!-- 物理机NC异常事件 -->
        <div class="bg-slate-800 p-6 rounded-lg border border-slate-700">
            <h3 class="text-xl font-semibold text-sky-400 mb-2">⚙️ 物理机NC异常事件</h3>
            <ul class="space-y-3 text-sm text-slate-400">
                <li class="pl-2 border-l-2 border-green-500"><strong class="text-white">健康:</strong> 实例 <code class="mono text-sm">i-2zej47d699e60ikj68vv</code> 所在宿主机 <code class="mono text-sm">**************</code> 在时间窗口 <code class="mono text-xs">2025-08-27 22:14:01 至 2025-08-28 02:14:01</code> 内无任何异常事件记录。</li>
                <li class="pl-2 border-l-2 border-green-500"><strong class="text-white">健康:</strong> 相关物理机硬件状态正常，无故障告警。</li>
                <li class="mt-4 pt-4 border-t border-slate-700 text-slate-500">结论：相关物理机均无硬件故障或异常，底层基础设施稳定，排除物理层面对实例的影响。</li>
            </ul>
        </div>

        <!-- 客户侧运维事件 -->
        <div class="bg-slate-800 p-6 rounded-lg border border-slate-700">
            <h3 class="text-xl font-semibold text-sky-400 mb-2">👤 客户侧运维事件</h3>
            <ul class="space-y-3 text-sm text-slate-400">
                <li class="pl-2 border-l-2 border-green-500"><strong class="text-white">操作记录:</strong> 查询时间窗口内未发现客户主动运维操作记录。</li>
                <li class="pl-2 border-l-2 border-green-500"><strong class="text-white">配置变更:</strong> 查询时间窗口内未发现客户配置变更记录。</li>
            </ul>
        </div>

        <!-- 底座基础设施变更 -->
        <div class="bg-slate-800 p-6 rounded-lg border border-slate-700">
            <h3 class="text-xl font-semibold text-sky-400 mb-2">🏗️ 底座基础设施变更</h3>
            <ul class="space-y-3 text-sm text-slate-400">
                <li class="pl-2 border-l-2 border-green-500"><strong class="text-white">系统变更:</strong> 物理机 <code class="mono text-sm">**************</code> 在时间窗口 <code class="mono text-xs">2025-08-27 22:14:01 至 2025-08-28 02:14:01</code> 内未发生任何高危变更记录。</li>
                <li class="pl-2 border-l-2 border-green-500"><strong class="text-white">补丁更新:</strong> 物理机 <code class="mono text-sm">**************</code> 在时间窗口 <code class="mono text-xs">2025-08-27 22:14:01 至 2025-08-28 02:14:01</code> 内未发现补丁更新记录。</li>
            </ul>
        </div>
    </div>
</section>
                
            </div>
        
    </section>

    <!-- Slide 5: 支撑证据 -->
    <section class="slide min-h-screen flex-col justify-center px-6 md:px-20 py-12 relative compact-section" data-slide="4">
        
            <div class="space-y-4 flex-grow flex flex-col justify-center compact-spacing">
                
                    <section class="min-h-screen flex flex-col justify-center px-6 md:px-20 py-16 relative">
    <header class="pb-4 border-b border-slate-700 mb-2">
        <h2 class="text-3xl font-bold text-white">故障事件线</h2>
        <p class="text-slate-400">围绕诊断资源的故障证据链分析</p>
    </header>
    <div class="flex-grow">
        <div class="space-y-3 max-w-6xl mx-auto">
            <div class="relative pl-8">
                <div class="absolute left-0 top-1.5 w-4 h-4 bg-sky-500 rounded-full border-4 border-slate-900"></div>
                <div class="absolute left-[7px] top-6 w-px h-full bg-slate-700"></div>
                <p class="text-slate-400 mono">2025-08-27 22:17:10</p>
                <h4 class="font-semibold text-white">虚拟机操作系统内核异常: vm_kernel:hang_task:fatal</h4>
                <p class="text-sm text-slate-500">证据: 虚拟机OS内部出现kernel异常可能导致夯死,请提交工单排查</p>
            </div>
            <div class="relative pl-8">
                <div class="absolute left-0 top-1.5 w-4 h-4 bg-yellow-500 rounded-full border-4 border-slate-900"></div>
                <div class="absolute left-[7px] top-6 w-px h-full bg-slate-700"></div>
                <p class="text-slate-400 mono">2025-08-28 00:13:00</p>
                <h4 class="font-semibold text-white">实例冷迁移开始: 从10.60.145.77迁移至10.250.205.214</h4>
                <p class="text-sm text-slate-500">证据: 冷迁移原因为启动前重调度(rechedule_before_vm_start)，状态为成功(recover_success)</p>
            </div>
            <div class="relative pl-8">
                <div class="absolute left-0 top-1.5 w-4 h-4 bg-sky-500 rounded-full border-4 border-slate-900"></div>
                 <div class="absolute left-[7px] top-6 w-px h-full bg-slate-700"></div>
                <p class="text-slate-400 mono">2025-08-28 00:14:01</p>
                <h4 class="font-semibold text-white">NC迁移事件: VM发生了NC的迁移</h4>
                <p class="text-sm text-slate-500">证据: 监控系统记录到vm_migrate_nc_event异常事件</p>
            </div>
            <div class="relative pl-8">
                <div class="absolute left-0 top-1.5 w-4 h-4 bg-green-500 rounded-full border-4 border-slate-900"></div>
                <p class="text-slate-400 mono">当前</p>
                <h4 class="font-semibold text-white">状态: 实例运行正常，物理机无异常记录</h4>
                <p class="text-sm text-slate-500">证据: 物理机**************在相关时间段内无异常事件、运维操作或变更记录</p>
            </div>
        </div>
    </div>
</section>
                
            </div>
        
    </section>

    <!-- Slide 6: 总结建议 -->
    <section class="slide min-h-screen flex-col justify-center px-6 md:px-20 py-12 relative compact-section" data-slide="5">
        
            <div class="space-y-4 flex-grow flex flex-col justify-center compact-spacing">
                
                    <section class="min-h-screen flex flex-col justify-center px-6 md:px-20 py-16 relative">
    <header class="pb-4 border-b border-slate-700 mb-2">
        <h2 class="text-3xl font-bold text-white">详细资料信息 (附录)</h2>
        <p class="text-slate-400">实例基础数据与异常事件记录</p>
    </header>
    <div class="flex-grow space-y-3 flex flex-col justify-center">
        <!-- 实例基础数据 -->
        <div class="bg-slate-800 p-6 rounded-lg border border-slate-700">
            <h3 class="text-xl font-semibold text-sky-400 mb-4">💻 实例基础数据</h3>
            <div class="bg-blue-900 bg-opacity-20 rounded p-3 mb-3">
                <div class="text-blue-200 text-sm mb-2">实例配置、资源规格、网络配置等基础信息</div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-gray-600 rounded p-3">
                    <div class="text-blue-300 font-semibold mb-2">实例配置</div>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-300">实例ID:</span>
                            <span class="text-white font-mono">i-2zej47d699e60ikj68vv</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">实例规格:</span>
                            <span class="text-yellow-300">ecs.u1-c1m2.xlarge</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">CPU核数:</span>
                            <span class="text-yellow-300">4核</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">存储类型:</span>
                            <span class="text-yellow-300">云盘</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">操作系统:</span>
                            <span class="text-yellow-300">CentOS_64</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">创建时间:</span>
                            <span class="text-blue-300 font-mono">2025-08-26 14:40:54</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">最近修改时间:</span>
                            <span class="text-blue-300 font-mono">2025-09-02 18:34:13</span>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-600 rounded p-3">
                    <div class="text-blue-300 font-semibold mb-2">网络与位置</div>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-300">私网IP:</span>
                            <span class="text-white font-mono">************</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">网络类型:</span>
                            <span class="text-yellow-300">VPC</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">可用区:</span>
                            <span class="text-yellow-300">cn-beijing-l</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">用户ID:</span>
                            <span class="text-white font-mono">1739641830090024</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">物理机IP:</span>
                            <span class="text-white font-mono">**************</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">物理机ID:</span>
                            <span class="text-white font-mono">34474-215</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">实例状态:</span>
                            <span class="text-green-400">Running</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="text-xs text-gray-400 mt-3">来源: 实例元数据、网络配置</div>
        </div>

        <!-- 异常事件数据 -->
        <div class="w-full bg-gray-700 rounded-lg p-4 border-l-4 border-red-500 mb-6">
            <h3 class="text-lg font-semibold text-red-300 mb-3 flex items-center">
                <span class="w-2 h-2 bg-red-300 rounded-full mr-2"></span>
                ⚠️ 异常事件数据
            </h3>
            <div class="bg-red-900 bg-opacity-20 rounded p-3 mb-3">
                <div class="text-red-200 text-sm mb-2">错误日志、告警记录、性能异常等关键异常信息（包含资源ID、异常名称、额外信息）</div>
            </div>

            <!-- 异常事件表格 -->
            <div class="overflow-x-auto">
                <table class="w-full text-sm border-collapse">
                    <thead>
                        <tr class="bg-gray-600">
                            <th class="border border-gray-500 p-2 text-red-300 text-left">资源ID</th>
                            <th class="border border-gray-500 p-2 text-red-300 text-left">异常名称</th>
                            <th class="border border-gray-500 p-2 text-red-300 text-left">发生时间</th>
                            <th class="border border-gray-500 p-2 text-red-300 text-left">关键信息</th>
                            <th class="border border-gray-500 p-2 text-red-300 text-left">严重程度</th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-200">
                        <tr class="bg-gray-650">
                            <td class="border border-gray-500 p-2 font-mono">i-2zej47d699e60ikj68vv</td>
                            <td class="border border-gray-500 p-2">vm_kernel:hang_task:fatal</td>
                            <td class="border border-gray-500 p-2 font-mono text-blue-300">2025-08-27 22:17:10</td>
                            <td class="border border-gray-500 p-2">
                                <span class="bg-red-500 text-white px-2 py-1 rounded text-xs">虚拟机操作系统内核异常</span><br>
                                <span class="text-yellow-300">进程夯异常: VmExit指标异常疑似虚拟机内部夯死或者蓝屏</span><br>
                                <span class="text-gray-300">原因: 虚拟机操作系统</span>
                            </td>
                            <td class="border border-gray-500 p-2 text-red-400 font-semibold">严重</td>
                        </tr>
                        <tr class="bg-gray-700">
                            <td class="border border-gray-500 p-2 font-mono">i-2zej47d699e60ikj68vv</td>
                            <td class="border border-gray-500 p-2">vm_migrate_nc_event</td>
                            <td class="border border-gray-500 p-2 font-mono text-blue-300">2025-08-28 00:14:01</td>
                            <td class="border border-gray-500 p-2">
                                <span class="text-yellow-300">VM发生了NC的迁移</span><br>
                                <span class="text-gray-300">共发生4次</span><br>
                                <span class="text-gray-300">首次时间: 2025-08-28 00:14:01</span><br>
                                <span class="text-gray-300">最后时间: 2025-09-01 00:34:02</span>
                            </td>
                            <td class="border border-gray-500 p-2 text-yellow-400 font-semibold">警告</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="text-xs text-gray-400 mt-3">来源: 系统日志、监控告警、错误日志</div>
        </div>

        <!-- 冷迁移记录数据 -->
        <div class="w-full bg-gray-700 rounded-lg p-4 border-l-4 border-yellow-500 mb-6">
            <h3 class="text-lg font-semibold text-yellow-300 mb-3 flex items-center">
                <span class="w-2 h-2 bg-yellow-300 rounded-full mr-2"></span>
                ❄️ 冷迁移记录数据
            </h3>
            <div class="bg-yellow-900 bg-opacity-20 rounded p-3 mb-3">
                <div class="text-yellow-200 text-sm mb-2">冷迁移操作的完整记录和状态变化（包含实例ID、迁移原因等）</div>
            </div>

            <!-- 迁移记录表格 -->
            <div class="overflow-x-auto">
                <table class="w-full text-sm border-collapse">
                    <thead>
                        <tr class="bg-gray-600">
                            <th class="border border-gray-500 p-2 text-yellow-300 text-left">实例ID</th>
                            <th class="border border-gray-500 p-2 text-yellow-300 text-left">迁移类型</th>
                            <th class="border border-gray-500 p-2 text-yellow-300 text-left">源物理机</th>
                            <th class="border border-gray-500 p-2 text-yellow-300 text-left">目标物理机</th>
                            <th class="border border-gray-500 p-2 text-yellow-300 text-left">迁移原因</th>
                            <th class="border border-gray-500 p-2 text-yellow-300 text-left">状态</th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-200">
                        <tr class="bg-gray-650">
                            <td class="border border-gray-500 p-2 font-mono">i-2zej47d699e60ikj68vv</td>
                            <td class="border border-gray-500 p-2">冷迁移</td>
                            <td class="border border-gray-500 p-2 font-mono">10.60.145.77</td>
                            <td class="border border-gray-500 p-2 font-mono">10.250.205.214</td>
                            <td class="border border-gray-500 p-2 text-yellow-300">启动前重调度<br><span class="text-xs text-gray-300">(rechedule_before_vm_start)</span></td>
                            <td class="border border-gray-500 p-2 text-green-400 font-semibold">成功<br><span class="text-xs text-gray-300">(recover_success)</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 迁移时间线 -->
            <div class="mt-4 bg-gray-600 rounded p-3">
                <div class="text-yellow-300 font-semibold mb-2">迁移时间线详情</div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
                    <div class="text-center">
                        <div class="text-blue-300 font-mono mb-1">2025-08-28 00:13</div>
                        <div class="text-blue-400">迁移开始</div>
                        <div class="text-gray-300">启动前重调度</div>
                    </div>
                    <div class="text-center">
                        <div class="text-blue-300 font-mono mb-1">2025-08-28 00:14</div>
                        <div class="text-green-400">迁移完成</div>
                        <div class="text-gray-300">成功恢复</div>
                    </div>
                </div>
            </div>
            <div class="text-xs text-gray-400 mt-3">来源: 迁移管理系统、操作日志</div>
        </div>

        <!-- 客户运维数据 -->
        <div class="w-full bg-gray-700 rounded-lg p-4 border-l-4 border-green-500">
            <h3 class="text-lg font-semibold text-green-300 mb-3 flex items-center">
                <span class="w-2 h-2 bg-green-300 rounded-full mr-2"></span>
                👤 客户运维数据
            </h3>
            <div class="bg-green-900 bg-opacity-20 rounded p-3 mb-3">
                <div class="text-green-200 text-sm mb-2">用户操作记录、配置变更、业务操作等（包含运维记录、系统操作记录、用户操作记录）</div>
            </div>

            <!-- 运维记录表格 -->
            <div class="overflow-x-auto">
                <table class="w-full text-sm border-collapse">
                    <thead>
                        <tr class="bg-gray-600">
                            <th class="border border-gray-500 p-2 text-green-300 text-left">操作类型</th>
                            <th class="border border-gray-500 p-2 text-green-300 text-left">操作者</th>
                            <th class="border border-gray-500 p-2 text-green-300 text-left">操作时间</th>
                            <th class="border border-gray-500 p-2 text-green-300 text-left">操作详情</th>
                            <th class="border border-gray-500 p-2 text-green-300 text-left">源IP/来源</th>
                            <th class="border border-gray-500 p-2 text-green-300 text-left">状态</th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-200">
                        <tr class="bg-gray-650">
                            <td class="border border-gray-500 p-2">系统维护</td>
                            <td class="border border-gray-500 p-2 text-blue-300">系统</td>
                            <td class="border border-gray-500 p-2 font-mono text-blue-300">2025-08-28 00:13</td>
                            <td class="border border-gray-500 p-2">
                                <span class="text-yellow-300 font-semibold">冷迁移</span><br>
                                <span class="text-gray-300 text-xs">启动前重调度</span>
                            </td>
                            <td class="border border-gray-500 p-2 font-mono">系统自动</td>
                            <td class="border border-gray-500 p-2 text-green-400 font-semibold">完成</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 操作统计 -->
            <div class="mt-4 bg-gray-600 rounded p-3">
                <div class="text-green-300 font-semibold mb-2">操作统计概览</div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-300">1</div>
                        <div class="text-gray-300">系统操作</div>
                        <div class="text-xs text-gray-400">冷迁移</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-yellow-300">0</div>
                        <div class="text-gray-300">用户操作</div>
                        <div class="text-xs text-gray-400">手动配置</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-300">100%</div>
                        <div class="text-gray-300">操作成功率</div>
                        <div class="text-xs text-gray-400">无失败操作</div>
                    </div>
                </div>
            </div>
            <div class="text-xs text-gray-400 mt-3">来源: 用户操作日志、配置变更记录、系统审计日志</div>
        </div>
    </div>
</section>
                
            </div>
        
    </section>


<script>
(function() {
    let currentSlideIndex = 0;
    let slides = document.querySelectorAll('.slide');
    let totalSlides = slides.length;
    
    console.log('总幻灯片数:', totalSlides);
    
    // 更新幻灯片显示
    function updateSlideDisplay() {
        console.log('更新显示，当前索引:', currentSlideIndex);
        
        // 更新页码显示
        document.getElementById('currentSlide').textContent = currentSlideIndex + 1;
        
        // 更新进度条
        const progressPercent = ((currentSlideIndex + 1) / totalSlides) * 100;
        document.getElementById('progressBar').style.width = progressPercent + '%';
        
        // 隐藏所有幻灯片，显示当前幻灯片
        slides.forEach((slide, index) => {
            if (index === currentSlideIndex) {
                slide.classList.add('active');
            } else {
                slide.classList.remove('active');
            }
        });
        
        // 更新点导航
        document.querySelectorAll('.slide-dot').forEach((dot, index) => {
            if (index === currentSlideIndex) {
                dot.classList.add('active');
            } else {
                dot.classList.remove('active');
            }
        });
        
        // 更新按钮状态
        document.getElementById('prevBtn').disabled = currentSlideIndex === 0;
        document.getElementById('nextBtn').disabled = currentSlideIndex === totalSlides - 1;
    }
    
    // 下一张幻灯片
    function nextSlide() {
        console.log('点击下一页，当前索引:', currentSlideIndex);
        if (currentSlideIndex < totalSlides - 1) {
            currentSlideIndex++;
            updateSlideDisplay();
        }
    }
    
    // 上一张幻灯片
    function prevSlide() {
        console.log('点击上一页，当前索引:', currentSlideIndex);
        if (currentSlideIndex > 0) {
            currentSlideIndex--;
            updateSlideDisplay();
        }
    }
    
    // 跳转到指定幻灯片
    function goToSlide(index) {
        console.log('跳转到页面:', index);
        if (index >= 0 && index < totalSlides) {
            currentSlideIndex = index;
            updateSlideDisplay();
        }
    }
    
    // 事件监听器
    document.getElementById('nextBtn').addEventListener('click', function(e) {
        e.preventDefault();
        console.log('下一页按钮被点击');
        nextSlide();
    });
    
    document.getElementById('prevBtn').addEventListener('click', function(e) {
        e.preventDefault();
        console.log('上一页按钮被点击');
        prevSlide();
    });
    
    // 点导航事件
    document.querySelectorAll('.slide-dot').forEach((dot, index) => {
        dot.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('点导航被点击，索引:', index);
            goToSlide(index);
        });
    });
    
    // 键盘导航
    document.addEventListener('keydown', (e) => {
        switch(e.key) {
            case 'ArrowLeft':
            case 'ArrowUp':
                e.preventDefault();
                prevSlide();
                break;
            case 'ArrowRight':
            case 'ArrowDown':
                e.preventDefault();
                nextSlide();
                break;
            case 'Home':
                e.preventDefault();
                goToSlide(0);
                break;
            case 'End':
                e.preventDefault();
                goToSlide(totalSlides - 1);
                break;
        }
    });
    
    // 初始化
    console.log('初始化幻灯片');
    updateSlideDisplay();
    
})();
</script>

</body>
</html>