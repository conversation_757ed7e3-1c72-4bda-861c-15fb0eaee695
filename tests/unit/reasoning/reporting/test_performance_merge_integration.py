#!/usr/bin/env python3
"""
性能数据合并集成测试
测试将真实性能数据合并到原始报告中，验证幻灯片分页系统的正确性
"""

import pytest
import asyncio
import os
import re
from pathlib import Path

from deep_diagnose.core.reasoning.services.merge.report_generation_service import ReportGenerationService


class TestPerformanceMergeIntegration:
    """性能数据合并集成测试"""
    
    @pytest.mark.asyncio
    async def test_merge_performance_data_with_original_report(self):
        """
        测试将性能数据合并到原始报告中
        
        验证要点:
        1. 性能数据成功集成到幻灯片系统
        2. 导航系统正确更新
        3. 原始内容完全保留
        4. HTML结构完整
        """
        # 测试数据路径
        test_dir = Path(__file__).parent
        original_report_path = test_dir / "rule_merge_result.html"
        request_id = "req_4243f011-3a8b-4802-9c96-abb1057154bd"
        
        # 读取原始报告
        with open(original_report_path, 'r', encoding='utf-8') as f:
            original_html = f.read()
        
        # 创建报告生成服务，指定正确的数据路径
        from deep_diagnose.core.reasoning.services.performance_service import PerformanceService
        from deep_diagnose.core.reasoning.services.html_service import HtmlService
        import tempfile
        import shutil
        
        # 设置正确的数据路径 
        # 性能服务期望的路径结构是: base_dir/file_system/request_id/
        # 但测试数据直接在 test_dir/request_id/ 下
        # 所以我们需要创建一个临时的符合期望结构的路径
        temp_dir = Path(tempfile.mkdtemp())
        try:
            # 创建期望的目录结构: temp_dir/file_system/request_id/
            expected_data_dir = temp_dir / "file_system" / request_id
            expected_data_dir.mkdir(parents=True, exist_ok=True)
            
            # 复制测试数据到期望位置
            source_data_dir = test_dir / request_id
            if source_data_dir.exists():
                for file_path in source_data_dir.iterdir():
                    shutil.copy2(file_path, expected_data_dir / file_path.name)
                print(f"📁 测试数据已复制到: {expected_data_dir}")
            
            performance_service = PerformanceService(base_dir=str(temp_dir))
            html_service = HtmlService()
            
            # 创建自定义的报告生成服务
            report_service = ReportGenerationService(
                performance_service=performance_service,
                html_service=html_service
            )
            
            # 执行性能数据合并
            merged_html = await report_service.merge_performance_data(original_html, request_id)
            
        finally:
            # 清理临时目录
            shutil.rmtree(temp_dir, ignore_errors=True)
        
        # 基础验证：合并成功
        assert merged_html is not None
        assert len(merged_html) >= len(original_html)
        
        # 验证HTML结构完整性
        assert "<!DOCTYPE html>" in merged_html
        assert merged_html.count("<html") >= 1
        assert merged_html.count("</html>") >= 1
        assert merged_html.count("<body") >= 1
        assert merged_html.count("</body>") >= 1
        
        # 验证原始内容保留
        original_key_content = [
            "ECS 实例操作系统内核异常诊断",
            "i-2zej47d699e60ikj68vv",
            "vm_kernel:hang_task:fatal",
            "问题概述",
            "关键发现"
        ]
        
        for content in original_key_content:
            assert content in merged_html, f"原始内容'{content}'在合并后丢失"
        
        # 验证性能数据存在
        performance_indicators = [
            "性能数据",
            "附录A",
            "performance",
            "CPU",
            "内存"
        ]
        
        found_indicators = [indicator for indicator in performance_indicators if indicator in merged_html]
        assert len(found_indicators) > 0, f"未找到性能数据指标，检查的指标: {performance_indicators}"
        
        # 验证幻灯片系统完整性
        slide_sections = re.findall(r'<section[^>]*class="[^"]*slide[^"]*"', merged_html)
        original_slides = re.findall(r'<section[^>]*class="[^"]*slide[^"]*"', original_html)
        
        # 检查是否成功添加了性能数据
        has_performance_section = any(indicator in merged_html for indicator in ["附录A. 性能数据分析", "性能数据"])
        
        if has_performance_section:
            # 如果性能数据成功添加，验证slide数量增加
            assert len(slide_sections) == len(original_slides) + 1, \
                f"性能数据slide未正确添加：原始{len(original_slides)}个，合并后{len(slide_sections)}个"
            print(f"✅ 性能数据已成功添加为新的slide")
        else:
            # 如果性能数据未添加（可能因为数据文件缺失），验证原始内容不变
            assert len(slide_sections) == len(original_slides), \
                f"原始slide结构被意外修改：原始{len(original_slides)}个，当前{len(slide_sections)}个"
            print(f"⚠️  性能数据未添加（可能缺少数据文件），但原始报告保持完整")
        
        # 验证data-slide属性连续性
        data_slide_attrs = re.findall(r'data-slide="(\d+)"', merged_html)
        if data_slide_attrs:
            unique_indices = sorted(set(int(attr) for attr in data_slide_attrs))
            expected_indices = list(range(len(unique_indices)))
            assert unique_indices == expected_indices, f"Slide索引不连续: {unique_indices}"
        
        # 验证导航点数量（如果存在）
        slide_dots = re.findall(r'<div class="slide-dot(?:\s+[^"]*)?"\s+data-slide="\d+"', merged_html)
        unique_slide_count = len(set(data_slide_attrs))
        
        if slide_dots and unique_slide_count > 0:
            assert len(slide_dots) == unique_slide_count, \
                f"导航点数量({len(slide_dots)})与slide数量({unique_slide_count})不匹配"
        
        # 验证总页数更新（如果有JavaScript控制）
        js_total_slides = re.search(r'let totalSlides = (\d+)', merged_html)
        if js_total_slides:
            total_slides_js = int(js_total_slides.group(1))
            assert total_slides_js == unique_slide_count, \
                f"JavaScript总页数({total_slides_js})与实际slide数量({unique_slide_count})不匹配"
        
        # 验证实例ID存在（仅当性能数据成功添加时）
        if has_performance_section:
            test_data_dir = test_dir / request_id
            if test_data_dir.exists():
                json_files = list(test_data_dir.glob("vm_performance_*.json"))
                for json_file in json_files:
                    instance_id_match = re.search(r'vm_performance_(i-[a-z0-9]+)_', json_file.name)
                    if instance_id_match:
                        instance_id = instance_id_match.group(1)
                        assert instance_id in merged_html, f"实例ID {instance_id} 未在性能报告中找到"
                        print(f"✅ 实例ID {instance_id} 已在性能报告中找到")
        else:
            print(f"⚠️  跳过实例ID验证（性能数据未添加）")
        
        # 保存合并结果用于调试
        output_path = test_dir / "performance_merge_integration_result.html"
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(merged_html)
        
        print(f"✅ 性能数据合并集成测试通过")
        print(f"✅ 原始内容完全保留")
        print(f"✅ 性能数据成功集成")
        print(f"✅ 幻灯片系统正确更新")
        print(f"✅ 合并结果已保存到: {output_path}")


if __name__ == "__main__":
    # 运行单个测试
    pytest.main([__file__, "-v", "--tb=short"])