#!/usr/bin/env python3

import pytest
import asyncio
import os

from deep_diagnose.core.reasoning.services.merge.report_generation_service import (
    RuleBasedMerger,
    MergeContext,
    ReportGenerationError
)
from deep_diagnose.core.reasoning.services.merge.html_processing_service import HtmlProcessingService
from deep_diagnose.core.reasoning.workflow.types import ReasoningState


class TestReportMerge:
    
    @pytest.fixture
    def sample_sections(self):
        """从HTML文件加载样例HTML片段"""
        base_dir = os.path.dirname(__file__)
        sections = {}
        
        # 定义文件映射
        file_mapping = {
            "problem_cover_html": "problem_cover_html.html",
            "problem_description_html": "problem_description_html.html", 
            "diagnosis_info_html": "diagnosis_info_html.html",
            "key_findings_html": "key_findings_html.html",
            "evidence_chain_html": "evidence_chain_html.html",
            "summary_conclusion_html": "summary_conclusion_html.html"
        }
        
        # 读取每个HTML文件
        for key, filename in file_mapping.items():
            file_path = os.path.join(base_dir, filename)
            with open(file_path, 'r', encoding='utf-8') as f:
                sections[key] = f.read()
        
        return sections
    
    @pytest.fixture
    def sample_state(self):
        """样例推理状态"""
        return ReasoningState({
            "final_report": "最终报告内容",
            "observations": ["观察1", "观察2"],
            "current_plan": "当前计划",
            "messages": ["用户查询内容"]
        })
    
    @pytest.fixture
    def html_processing_service(self):
        """真实的HTML处理服务"""
        return HtmlProcessingService()
    
    @pytest.mark.asyncio 
    async def test_rule_based_merge_success(self, sample_sections, sample_state, html_processing_service):
        """测试规则合并成功场景"""
        # 准备测试数据
        user_query = "测试查询"
        context = MergeContext(sample_sections, sample_state, user_query, html_processing_service)
        
        # 创建规则合并器
        merger = RuleBasedMerger()
        
        # 执行合并
        result = await merger.merge(context)
        
        # 验证结果
        assert result is not None
        assert len(result) > 0
        assert "<!DOCTYPE html>" in result
        assert "<html" in result
        assert "<body" in result
        
        # 验证来自HTML文件的真实内容
        assert "ECS 实例操作系统内核异常诊断" in result  # 来自problem_cover_html.html
        assert "i-2zej47d699e60ikj68vv" in result  # 实例ID
        assert "vm_kernel:hang_task:fatal" in result  # 异常类型
        assert "2025-08-27 22:17:10" in result  # 异常时间
        assert "问题摘要" in result  # 来自problem_description_html.html
        assert "关键结论" in result  # 来自diagnosis_info_html.html
        assert "关键发现" in result  # 来自key_findings_html.html
        assert "故障事件线" in result  # 来自evidence_chain_html.html
        assert "详细资料信息" in result  # 来自summary_conclusion_html.html
        assert "RCA 报告: 最终报告内容" in result
    
    @pytest.mark.asyncio
    async def test_llm_merge_fallback_to_rule(self, sample_sections, sample_state, html_processing_service):
        """测试LLM合并失败时回退到规则合并"""
        from deep_diagnose.core.reasoning.services.merge.report_generation_service import ReportGenerationService
        
        # 准备测试数据
        user_query = "复杂的测试查询场景"
        
        # 创建报告生成服务
        service = ReportGenerationService(
            html_processing_service=html_processing_service,
            llm_retry_timeouts=[0.1]  # 设置很短的超时时间来触发失败
        )
        
        # 执行合并，应该会从LLM回退到规则合并
        result = await service.generate_complete_report(sample_sections, sample_state, user_query)
        
        # 验证结果 - 即使LLM失败，规则合并应该成功
        assert result is not None
        assert len(result) > 0
        assert "<!DOCTYPE html>" in result
        assert "<html" in result
        assert "<body" in result
        
        # 验证关键内容存在（应该使用规则合并生成）
        assert "ECS 实例操作系统内核异常诊断" in result
        assert "i-2zej47d699e60ikj68vv" in result
        assert "vm_kernel:hang_task:fatal" in result
    
    @pytest.mark.asyncio
    async def test_real_llm_merge_with_output(self, sample_sections, sample_state, html_processing_service):
        """测试真实的LLM合并，并保存结果到文件"""
        from deep_diagnose.core.reasoning.services.merge.report_generation_service import ReportGenerationService
        
        # 准备测试数据
        user_query = "请分析ECS实例i-2zej47d699e60ikj68vv的操作系统内核异常问题"
        
        # 创建报告生成服务 - 使用真实的LLM超时时间
        service = ReportGenerationService(
            html_processing_service=html_processing_service,
            llm_retry_timeouts=[60.0, 30.0, 15.0]  # 真实的超时时间，允许LLM调用
        )
        
        # 执行LLM合并（这会实际调用LLM）
        try:
            result = await service.generate_complete_report(sample_sections, sample_state, user_query)
            
            # 保存LLM合并结果
            output_dir = os.path.dirname(__file__)
            llm_output_path = os.path.join(output_dir, "llm_merge_result.html")
            with open(llm_output_path, 'w', encoding='utf-8') as f:
                f.write(result)
            print(f"✅ LLM合并结果已保存到: {llm_output_path}")
            
            # 验证LLM合并结果
            assert result is not None
            assert len(result) > 0
            assert "<!DOCTYPE html>" in result
            assert "<html" in result
            assert "<body" in result
            assert "i-2zej47d699e60ikj68vv" in result
            
        except Exception as e:
            print(f"⚠️  LLM合并失败，将使用规则合并作为回退: {e}")
            # 如果LLM失败，测试回退机制
            result = await service.generate_complete_report(sample_sections, sample_state, user_query)
            
            # 保存回退结果
            output_dir = os.path.dirname(__file__)
            fallback_output_path = os.path.join(output_dir, "fallback_merge_result.html")
            with open(fallback_output_path, 'w', encoding='utf-8') as f:
                f.write(result)
            print(f"✅ 回退合并结果已保存到: {fallback_output_path}")
            
            # 验证回退结果
            assert result is not None
            assert len(result) > 0
            assert "<!DOCTYPE html>" in result
    
    @pytest.mark.asyncio
    async def test_save_rule_merge_result(self, sample_sections, sample_state, html_processing_service):
        """测试规则合并并保存结果到文件"""
        # 准备测试数据
        user_query = "规则合并测试查询"
        context = MergeContext(sample_sections, sample_state, user_query, html_processing_service)
        
        # 创建规则合并器
        merger = RuleBasedMerger()
        
        # 执行合并
        result = await merger.merge(context)
        
        # 保存规则合并结果
        output_dir = os.path.dirname(__file__)
        rule_output_path = os.path.join(output_dir, "rule_merge_result.html")
        with open(rule_output_path, 'w', encoding='utf-8') as f:
            f.write(result)
        print(f"✅ 规则合并结果已保存到: {rule_output_path}")
        
        # 验证结果
        assert result is not None
        assert len(result) > 0
        assert "<!DOCTYPE html>" in result
        assert "ECS 实例操作系统内核异常诊断" in result
        assert "i-2zej47d699e60ikj68vv" in result