"""
Agent Manager 单元测试

测试Agent管理器的核心功能，特别是prepare_agent_kwargs方法
"""

import sys
import os
import pytest
from unittest.mock import Mock, MagicMock
from typing import Dict, Any, Optional, List

# 导入被测试模块
from deep_diagnose.services.chat.managers.agent_manager import Agent<PERSON><PERSON><PERSON>
from deep_diagnose.services.chat.managers.session_manager import SessionInfo, ChatContext


class MockContext:
    """模拟请求上下文"""

    def __init__(
        self,
        question: str = "测试问题",
        user_id: str = "test_user",
        agent: str = "TestAgent",
        request_id: str = "test_request_123",
        kwargs: Optional[Dict[str, Any]] = None
    ):
        self.question = question
        self.user_id = user_id
        self.agent = agent
        self.request_id = request_id
        self.kwargs = kwargs or {}


class TestAgentManager:
    """Agent管理器测试类"""

    @pytest.fixture
    def mock_session_info(self):
        """模拟会话信息"""
        return SessionInfo(
            session_id="test_session_123",
            message_id=456,
            messages=[],
            user_id="test_user",
            source="api",
            resource_ids=["resource_1", "resource_2"],
            resource_type=["ecs", "rds"],
            ext={"key": "value"}
        )

    @pytest.fixture
    def mock_context(self):
        """模拟上下文对象"""
        return MockContext()

    def test_prepare_agent_kwargs_basic_functionality(self, mock_context, mock_session_info):
        """测试prepare_agent_kwargs基本功能"""
        # 执行方法
        result = AgentManager.prepare_agent_kwargs(mock_context, mock_session_info)

        # 验证基本属性设置
        assert result["request_id"] == mock_context.request_id
        assert result["user_id"] == mock_context.user_id
        assert "resource_ids" not in result  # 非特定Agent类型不应设置这些属性
        assert "resource_type" not in result
        assert "ext" not in result
        assert "session_id" not in result

    def test_prepare_agent_kwargs_with_existing_kwargs(self, mock_session_info):
        """测试prepare_agent_kwargs处理现有kwargs"""
        # 设置包含现有参数的上下文
        existing_kwargs = {
            "existing_param": "existing_value",
            "another_param": 123
        }
        context = MockContext(kwargs=existing_kwargs)

        # 执行方法
        result = AgentManager.prepare_agent_kwargs(context, mock_session_info)

        # 验证现有参数被保留
        assert result["existing_param"] == "existing_value"
        assert result["another_param"] == 123
        # 验证新参数被添加
        assert result["request_id"] == context.request_id
        assert result["user_id"] == context.user_id

    def test_prepare_agent_kwargs_interactive_agent(self, mock_session_info):
        """测试InteractiveAgent的prepare_agent_kwargs"""
        # 设置InteractiveAgent上下文
        context = MockContext(agent="InteractiveAgent", kwargs={"custom_param": "value"})

        # 执行方法
        result = AgentManager.prepare_agent_kwargs(context, mock_session_info)

        # 验证InteractiveAgent特定参数
        assert result["resource_ids"] == mock_session_info.resource_ids
        assert result["resource_type"] == mock_session_info.resource_type
        assert result["ext"] == mock_session_info.ext
        # 验证不包含InspectAgent特有参数
        assert "session_id" not in result
        # 验证原始问题未被修改
        assert context.question == "测试问题"

    def test_prepare_agent_kwargs_inspect_agent(self, mock_session_info):
        """测试InspectAgent的prepare_agent_kwargs"""
        # 设置InspectAgent上下文
        original_question = "原始问题"
        context = MockContext(agent="InspectAgent", question=original_question)

        # 执行方法
        result = AgentManager.prepare_agent_kwargs(context, mock_session_info)

        # 验证InspectAgent特定参数
        assert result["resource_ids"] == mock_session_info.resource_ids
        assert result["resource_type"] == mock_session_info.resource_type
        assert result["ext"] == mock_session_info.ext
        assert result["session_id"] == mock_session_info.session_id

        # 验证问题被修改为包含资源信息
        expected_prefix = f"资源ID: {mock_session_info.resource_ids} 资源类型: {mock_session_info.resource_type}\n\n"
        assert context.question.startswith(expected_prefix)
        assert context.question.endswith(original_question)

    def test_prepare_agent_kwargs_inspect_agent_empty_resources(self):
        """测试InspectAgent处理空资源信息"""
        # 创建空的SessionInfo
        session_info = SessionInfo(
            session_id="test_session",
            message_id=789,
            resource_ids=[],
            resource_type=[],
            ext={}
        )

        context = MockContext(agent="InspectAgent", question="测试问题")

        # 执行方法
        result = AgentManager.prepare_agent_kwargs(context, session_info)

        # 验证空资源信息被正确处理
        assert result["resource_ids"] == []
        assert result["resource_type"] == []
        assert result["ext"] == {}
        assert result["session_id"] == session_info.session_id

        # 验证问题格式（空列表的字符串表示）
        expected_prefix = "资源ID: [] 资源类型: []\n\n"
        assert context.question.startswith(expected_prefix)

    def test_prepare_agent_kwargs_other_agent_types(self):
        """测试其他Agent类型的通用处理"""
        # 测试不同Agent类型
        agent_types = ["ReasoningAgent", "PlanningAgent", "CustomAgent", ""]

        for agent_type in agent_types:
            context = MockContext(agent=agent_type)
            session_info = SessionInfo(
                session_id="test_session",
                message_id=100,
                resource_ids=["res1"],
                resource_type=["type1"]
            )

            result = AgentManager.prepare_agent_kwargs(context, session_info)

            # 验证基本参数
            assert result["request_id"] == context.request_id
            assert result["user_id"] == context.user_id

            # 验证不包含特定Agent的参数（除非是Interactive或Inspect）
            if agent_type not in ["InteractiveAgent", "InspectAgent"]:
                assert "resource_ids" not in result
                assert "resource_type" not in result
                assert "ext" not in result
                assert "session_id" not in result
                # 验证问题未被修改
                assert context.question == "测试问题"

    def test_prepare_agent_kwargs_request_id_fallback(self):
        """测试request_id的fallback逻辑"""
        # 创建没有request_id属性的上下文（删除属性）
        context = MockContext()
        context.request_id=None
        #delattr(context, 'request_id')  # 删除request_id属性来触发fallback

        session_info = SessionInfo(
            session_id="test_session",
            message_id=999,
            messages=[]
        )

        # 执行方法
        result = AgentManager.prepare_agent_kwargs(context, session_info)

        # 验证fallback到message_id的字符串形式
        assert result["request_id"] == str(session_info.message_id)

    def test_prepare_agent_kwargs_request_id_none(self):
        """测试request_id为None时的处理"""
        # 创建request_id为None的上下文
        context = MockContext()
        context.request_id = None  # 设置为None

        session_info = SessionInfo(
            session_id="test_session",
            message_id=999,
            messages=[]
        )

        # 执行方法
        result = AgentManager.prepare_agent_kwargs(context, session_info)

        # 验证返回None（因为属性存在但为None，getattr不会使用默认值）
        assert result["request_id"] is not None

    def test_prepare_agent_kwargs_request_id_from_context(self):
        """测试从context获取request_id"""
        # 创建带有request_id的上下文
        context = MockContext(request_id="custom_request_456")

        session_info = SessionInfo(
            session_id="test_session",
            message_id=123,
            messages=[]
        )

        # 执行方法
        result = AgentManager.prepare_agent_kwargs(context, session_info)

        # 验证使用context的request_id
        assert result["request_id"] == "custom_request_456"

    def test_prepare_agent_kwargs_complex_ext_data(self):
        """测试复杂的ext数据处理"""
        # 创建复杂的ext数据
        complex_ext = {
            "nested": {"key": "value"},
            "list": [1, 2, 3],
            "string": "test",
            "number": 42
        }

        session_info = SessionInfo(
            session_id="test_session",
            message_id=200,
            ext=complex_ext
        )

        context = MockContext(agent="InteractiveAgent")

        # 执行方法
        result = AgentManager.prepare_agent_kwargs(context, session_info)

        # 验证复杂ext数据被正确传递
        assert result["ext"] == complex_ext
        assert result["ext"]["nested"]["key"] == "value"
        assert result["ext"]["list"] == [1, 2, 3]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
