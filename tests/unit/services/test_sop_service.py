"""
Unit tests for SOPService.find_sop method.

Tests the find_sop functionality using real service implementations for e2e coverage.
"""

import pytest
from deep_diagnose.core.reasoning.planning.service import SOPService
from deep_diagnose.core.reasoning.planning.models import SelectedSOP
from deep_diagnose.core.reasoning.planning import create_sop_service


class TestSOPService:
    """Test cases for SOPService.find_sop method using real implementations."""
    

    
    @pytest.mark.asyncio
    async def test_find_sop_real_world_queries(self):
        """Test find_sop with real-world query examples from integration tests."""
        service = create_sop_service()
        
        test_queries = [
            "利用vmcore分析这个NC 26.52.185.141 在20250724的04:00宕机原因",
            "查询用户aliUid 1781574661016173的最近2天健康报告", 
            "你有哪些能力呢",
            "实例i-8vb1k5i5wwogeiozet2j i-bp1duea6t5ohk1umx7zk在2025-09-07 00:00:00至2025-09-07 05:00:00期间出现性能异",
            "实例性能问题"
        ]
        
        for query in test_queries:
            result = await service.find_sop(query)
            
            # Print detailed result information for each query
            print(f"\n查询: {query}")
            print(f"成功: {result.success}")
            print(f"原因: {result.reason}")
            if result.sop:
                print(f"选中的SOP: {result.sop.name} (ID: {result.sop.sop_id})")
                print(f"场景: {result.sop.scenario}")
            else:
                print("选中的SOP: None")
            if result.score is not None:
                print(f"分数: {result.score:.3f}")
            if result.error_message:
                print(f"错误信息: {result.error_message}")
            print("-" * 50)
            
            # Should return a result (success or failure depends on actual SOP data)
            assert isinstance(result, SelectedSOP)
            assert isinstance(result.success, bool)
            assert isinstance(result.reason, str)