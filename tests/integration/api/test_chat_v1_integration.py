"""

测试 Chat V1 API 返回的 SSE 事件，确保与 test_task_api_integration.py 中的 V1 Tasks API 返回的 SSE 事件完全一样。
"""

import json
from typing import Dict, Any, List, Optional
import httpx
import pytest
import os
import time
from datetime import datetime

# 设置测试环境
os.environ.setdefault('APP_ENV', 'test')


class ChatV1APITester:
    """Chat V1 API 测试器 - 专注于 SSE 事件兼容性"""
    
    def __init__(self, base_url: str = "http://localhost:8000", token: str = None):
        self.base_url = base_url
        self.token = token
        self.headers = {"Content-Type": "application/json"}
        if token:
            self.headers["Authorization"] = f"Bearer {token}"
    
    def _format_event_for_display(self, event: Dict[str, Any]) -> str:
        """将事件中的 data 字段（若为 JSON 字符串）进行 pretty JSON 展示，以提升可读性。
        保持其他字段不变。
        """
        try:
            # 浅拷贝，避免修改原对象
            display_event: Dict[str, Any] = dict(event)
            data_val = display_event.get("data")
            if isinstance(data_val, str):
                # 仅当明显是 JSON 时尝试解析
                trimmed = data_val.strip()
                if (trimmed.startswith("{") and trimmed.endswith("}")) or (
                    trimmed.startswith("[") and trimmed.endswith("]")
                ):
                    try:
                        display_event["data"] = json.loads(trimmed)
                    except Exception:
                        # 保持原样
                        pass
            return json.dumps(display_event, ensure_ascii=False, indent=2)
        except Exception:
            # 兜底：直接返回原始事件的字符串化
            return json.dumps(event, ensure_ascii=False, indent=2)
    
    async def authenticate(self) -> str:
        """获取认证token - 与 V1 API 相同的认证方式"""
        auth_data = {
            "access_key": "admin",
            "secret_key": "admin",
            "token_lifetime_minutes": 60
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/token",
                json=auth_data,
                headers={"Content-Type": "application/json"}
            )
            if response.status_code == 200:
                token_data = response.json()
                return token_data.get("access_token")
            else:
                raise Exception(f"Authentication failed: {response.status_code} - {response.text}")
    
    async def _stream_chat(self, request_data: Dict[str, Any], *, save_to_file: bool = True, file_prefix: str = "sse_events_integration") -> Dict[str, Any]:
        """通用的 SSE 事件流收集器，减少重复逻辑，并输出耗时统计信息。"""
        sse_events: List[str] = []
        parsed_events: List[Dict[str, Any]] = []
        first_token_seconds: Optional[float] = None
        stream_duration_seconds: Optional[float] = None
        content_type: Optional[str] = None

        # 创建输出目录（如果不存在）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = "tests/integration/api/sse_outputs"
        os.makedirs(output_dir, exist_ok=True)
        sse_file_path = f"{output_dir}/{file_prefix}_{timestamp}.txt"

        start_wall = datetime.now()
        start_perf = time.perf_counter()

        if save_to_file:
            print(f"\n🚀 开始调用 Chat V1 API...")
            q = request_data.get("question", "")
            print(f"📝 问题: {q[:100]}...")
            print(f"🕒 开始时间: {start_wall.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"📁 SSE 事件将保存到: {sse_file_path}")

        async with httpx.AsyncClient(timeout=httpx.Timeout(900.0)) as client:
            async with client.stream(
                "POST",
                f"{self.base_url}/api/v1/chat",
                json=request_data,
                headers=self.headers,
            ) as response:
                if response.status_code != 200:
                    raise Exception(
                        f"Chat V1 request failed: {response.status_code} - {await response.aread()}"
                    )

                content_type = response.headers.get("content-type", "")
                print(f"✅ 连接成功，Content-Type: {content_type}")
                print("=" * 80)

                last_event_perf = None
                async for line in response.aiter_lines():
                    if not line.strip():
                        continue
                    sse_events.append(line)

                    if line.startswith("data: "):
                        data_content = line[6:]
                        if data_content == "[DONE]":
                            # 当服务端发出结束标记，我们以最后一个事件时间作为结束
                            last_event_perf = last_event_perf or time.perf_counter()
                            break

                        try:
                            event_data = json.loads(data_content)
                            parsed_events.append(event_data)

                            # 记录 first token 延迟
                            if first_token_seconds is None:
                                first_token_seconds = time.perf_counter() - start_perf

                            # 更新最近事件时间
                            last_event_perf = time.perf_counter()

                            print(f"🚀 解析 SSE 事件 [{len(parsed_events):03d}]:\n" + self._format_event_for_display(event_data))
                        except json.JSONDecodeError as e:
                            print(f"Failed to parse event: {data_content}, error: {e}")
                            continue
                        except Exception as e:
                            print(f"Failed to others event: {data_content}, error: {e}")

                # 流结束后统计总耗时（从请求开始到最后一个事件）
                if last_event_perf is None:
                    # 没有收到任何 data 事件的情况
                    stream_duration_seconds = time.perf_counter() - start_perf
                else:
                    stream_duration_seconds = last_event_perf - start_perf

        # 打印统计信息
        if save_to_file:
            print(f"\n📊 SSE 事件接收完成！")
            print(f"📈 总事件数(行): {len(sse_events)}")
            print(f"📈 解析事件数: {len(parsed_events)}")
            if first_token_seconds is not None:
                print(f"⏱️ First token 耗时: {first_token_seconds:.3f}s")
            print(f"⏱️ 所有 SSE 事件耗时: {stream_duration_seconds:.3f}s")

        # 保存事件到文件
        if save_to_file and sse_events:
            try:
                self._write_sse_report(
                    sse_file_path,
                    request_data.get("question", ""),
                    sse_events,
                    parsed_events,
                    {
                        "content_type": content_type,
                        "first_token_seconds": first_token_seconds,
                        "stream_duration_seconds": stream_duration_seconds,
                        "sse_lines": len(sse_events),
                        "parsed_count": len(parsed_events),
                        "start_time": start_wall.strftime('%Y-%m-%d %H:%M:%S'),
                    },
                )
                print(f"✅ SSE 事件已保存到文件: {sse_file_path}")
            except Exception as e:
                print(f"⚠️  保存文件失败: {e}")

        return {
            "sse_events": sse_events,
            "parsed_events": parsed_events,
            "total_events": len(parsed_events),
            "file_path": sse_file_path if save_to_file else None,
            "metrics": {
                "first_token_seconds": first_token_seconds,
                "stream_duration_seconds": stream_duration_seconds,
                "content_type": content_type,
                "sse_lines": len(sse_events),
                "parsed_count": len(parsed_events),
            },
        }

    def _write_sse_report(
        self,
        file_path: str,
        question: str,
        raw_lines: List[str],
        parsed_events: List[Dict[str, Any]],
        metrics: Dict[str, Any],
    ) -> None:
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(f"# Chat V1 API SSE 事件记录 - Integration Test\n")
            f.write(f"# 时间: {metrics.get('start_time', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))}\n")
            f.write(f"# 问题: {question}\n")
            f.write(f"# Content-Type: {metrics.get('content_type', '')}\n")
            f.write(f"# SSE 总行数: {metrics.get('sse_lines', len(raw_lines))}\n")
            f.write(f"# 解析事件数: {metrics.get('parsed_count', len(parsed_events))}\n")
            if metrics.get("first_token_seconds") is not None:
                f.write(f"# First token 耗时: {metrics['first_token_seconds']:.3f}s\n")
            f.write(f"# 所有 SSE 事件耗时: {metrics.get('stream_duration_seconds', 0.0):.3f}s\n")
            f.write("# " + "=" * 70 + "\n\n")

            # 写入原始 SSE 事件
            f.write("# 原始 SSE 事件\n")
            f.write("# " + "=" * 70 + "\n\n")
            for i, event in enumerate(raw_lines, 1):
                f.write(f"[事件 {i:03d}] {event}\n")

            # 写入解析后的事件数据
            f.write("\n# " + "=" * 70 + "\n")
            f.write("# 解析后的事件数据 (JSON 格式)\n")
            f.write("# " + "=" * 70 + "\n\n")
            for i, event in enumerate(parsed_events, 1):
                f.write(f"# 解析事件 {i:03d}\n")
                f.write(self._format_event_for_display(event))
                f.write("\n\n")

            # 写入事件类型统计
            event_types: Dict[str, int] = {}
            for event in parsed_events:
                et = self._extract_event_type(event)
                event_types[et] = event_types.get(et, 0) + 1

            f.write("# " + "=" * 70 + "\n")
            f.write("# 事件类型统计\n")
            f.write("# " + "=" * 70 + "\n\n")
            for event_type, count in event_types.items():
                f.write(f"# {event_type}: {count} 次\n")

    def _extract_event_type(self, event: Dict[str, Any]) -> str:
        """兼容从顶层或嵌套 data 中获取 event_type。"""
        if "event_type" in event and isinstance(event["event_type"], str):
            return event["event_type"]
        # 尝试从 data 中获取
        data_val = event.get("data")
        try:
            if isinstance(data_val, str):
                trimmed = data_val.strip()
                if (trimmed.startswith("{") and trimmed.endswith("}")) or (
                    trimmed.startswith("[") and trimmed.endswith("]")
                ):
                    data_val = json.loads(trimmed)
        except Exception:
            pass
        if isinstance(data_val, dict) and isinstance(data_val.get("event_type"), str):
            return data_val["event_type"]
        return "unknown"

    async def test_chat_v1_sse_events(self, question: str, save_to_file: bool = True) -> Dict[str, Any]:
        """
        测试 Chat V1 API 的 SSE 事件（使用通用流式方法），并统计时延信息。
        """
        request_data = {
            "question": question,
            "agent": "ReasoningAgent",
            "user_id": "149789",
        }
        return await self._stream_chat(request_data, save_to_file=save_to_file, file_prefix="chat_v1")

    async def test_interactive_agent_sse_events(self, question: str, additional_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """测试 InteractiveAgent 的 SSE 事件，复用通用流式方法。"""
        request_data = {
            "question": question,
            "agent": "InteractiveAgent",
            "user_id": "149789",
            "additional_info": additional_info,
        }
        return await self._stream_chat(request_data, save_to_file=True, file_prefix="interactive_agent")


    async def test_inspect_agent_sse_events(self, question: str, additional_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """测试 InspectAgent 的 SSE 事件，复用通用流式方法。"""
        request_data = {
            "question": question,
            "agent": "InspectAgent",
            "user_id": "149789",
            "additional_info": additional_info,
        }
        return await self._stream_chat(request_data, save_to_file=True, file_prefix="inspect_agent")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_chat_v1_api_sse_events_e2e():
    """
    Chat V1 API SSE 事件端到端测试（需要服务器运行）
    
    运行此测试前，请确保：
    1. 启动后端服务器：python src/run.py
    2. 服务器运行在 http://localhost:8000
    """
    print("\n" + "="*80)
    print("开始 Chat V1 API SSE 事件端到端测试")
    print("="*80)
    
    # 初始化测试器
    tester = ChatV1APITester()
    
    print("🔐 正在进行身份认证...")
    try:
        token = await tester.authenticate()
        tester.token = token
        tester.headers["Authorization"] = f"Bearer {token}"
        print("✅ 身份认证成功")
    except Exception as e:
        pytest.skip(f"认证失败，跳过测试: {e}")
    
    # 准备测试数据

    #test_question="你有哪些能力呢"
    # 实例出现操作系统崩溃并重启
    test_question = """
    您好，这里将为您解决您刚在（苏州蜗牛数字科技股份有限公司企业标准服务）提出的企业工单，当前值班小二是[@峪森]，您描述的问题为（）。 2025-09-01 10:39:53 阿里云服务台 ### 聊天记录 (2025-09-01 10:39:45) --- 赫皓(2025-09-01 10:39:02): @阿里云服务台 ECS --- 阿里云服务台(2025-09-01 10:39:52): 已为您创建新企业工单 企业工单标题：ECS 企业工单ID：E250901513KDJ1 企业工单状态：处理中 当前小二：峪森 请 进入企业工单专项群。 2025-09-01 10:40:18 公共云TAM 您好，请问您具体想咨询关于ECS的什么问题或需求呢？ 2025-09-01 10:40:55 赫皓 > ###### 云小二 > 您好，请问您具体想咨询关于ECS的什� --------------- #### 实例ID：i-2zej47d699e60ikj68vv出现操作系统崩溃并重启，需要帮忙分析一下原因@云小二 2025-09-01 10:45:48 公共云TAM 您好，经排查：实例i-2zej47d699e60ikj68vv 底层未见异常，由于该实例操作系统内核panic导致系统重启。属于是操作系统崩溃引发的。 针对panic故障需要结合故障时收集到的vmcore进一步分析根因，如之前有配置过kdump建议您可以查看/var/crash/目录下是否生成vmcore，Windows系统可查看C:\目录下的dump文件。可以压缩发给我们分析。 2025-09-01 10:48:52 赫皓 > ###### 云小二 > 您好，经排查：实例i-2zej47d699e60ikj68vv --------------- #### @周闯 闯哥，你看下有vmcore文件嘛 2025-09-01 10:58:20 赫皓 如果没有这个文件的话，还可以通过什么进行分析一下了@峪森 2025-09-01 10:59:36 峪森 @赫皓 看不到原因的 2025-09-01 11:05:28 赫皓 > ###### 峪森 > @赫皓 看不到原因的 --------------- #### @峪森 基于之前的历史经验，有没有啥建议了 2025-09-01 11:09:39 峪森 @赫皓 主要是看不到os内宕机的根因，之前没配过kdump吗 2025-09-01 11:11:38 峪森 建议配下kdump以及hungtask_panic 2025-09-01 11:11:54 赫皓 这个需要闯哥帮忙确认一下@周闯 2025-09-02 10:26:56 峪森 有帮忙确认下吗 2025-09-02 15:59:39 赫皓 i-2zej47d699e60ikj68vv 2025-09-02 15:59:48 赫皓 这个机器好像现在一直在停止中 2025-09-02 16:00:04 赫皓 就是上面那个夯机的机器 2025-09-02 16:00:07 赫皓 @峪森 2025-09-02 16:01:13 赫皓 重启的时候等了大概15分钟，启动后还是刚开始的界面 2025-09-02 16:01:18 赫皓 https://yuntai-app-data.oss-cn-shanghai.aliyuncs.com/08480caa-44fa-4e7b-b54d-cc9aa920f5b2-20250902160120-191.png?Expires=4133952000&OSSAccessKeyId=LTAI5tCHjw6GESD5xjbRwYPN&Signature=pKt0fkA0UD6JWXOYQYWxeRDypM0%3D 2025-09-02 16:12:00 峪森 挂载分区有问题，能授权不 2025-09-02 16:12:45 赫皓 @周闯 闯哥，可以授权重新挂载一下哈 2025-09-02 16:32:59 赫皓 @丁贵清 这个群哈 2025-09-02 16:33:08 丁贵清 > ###### 赫皓 >@丁贵清 这个群哈 --------------- #### [OK] 2025-09-02 16:33:09 赫皓 @峪森 客户可以授权哈 2025-09-02 16:33:25 赫皓 重新挂载对原来的数据没有啥影响吧@峪森 2025-09-02 16:33:34 峪森 不会 2025-09-02 16:35:58 赫皓 授权链接发一下哈 2025-09-02 16:45:23 丁贵清 已经授权了 2025-09-02 16:45:58 赫皓 帮忙操作一下哈@峪森 2025-09-02 16:52:52 峪森 好的，稍等 2025-09-02 17:12:27 丁贵清 怎么样了 2025-09-02 17:17:13 峪森 还得稍等下 2025-09-02 17:30:04 峪森 这个机器帮忙对系统盘打个快照 2025-09-02 17:30:15 峪森 @丁贵清 2025-09-02 17:30:26 丁贵清 > ###### 峪森 >@丁贵清 --------------- #### [OK] 2025-09-02 17:34:09 丁贵清 快照已经好了 2025-09-02 17:36:11 峪森 好的 2025-09-02 18:00:12 丁贵清 现在进展如何了？ 2025-09-02 18:00:57 峪森 这个机器的grub有问题，引导不起来， 2025-09-02 18:01:05 峪森 还需要修下 2025-09-02 18:01:25 丁贵清 好的 2025-09-02 18:21:43 峪森 @丁贵清 修好了，正常了 2025-09-02 18:22:44 丁贵清 好的，我试一下 2025-09-02 18:22:54 峪森 https://yuntai-app-data.oss-cn-shanghai.aliyuncs.com/faeb4c98-f3b6-49e8-92a5-726b0e7c9e7d-20250902182255-512.png?Expires=4133952000&OSSAccessKeyId=LTAI5tCHjw6GESD5xjbRwYPN&Signature=ZM8JYoLWWlGE4Wf1o6RyNKZgql4%3D 2025-09-02 18:23:24 峪森 > ###### 丁贵清 >好的，我试一下 --------------- #### [OK] 2025-09-02 18:28:15 丁贵清 https://yuntai-app-data.oss-cn-shanghai.aliyuncs.com/14eb70ba-841b-4e00-b832-b0bc3283a8f4-20250902182817-541.png?Expires=4133952000&OSSAccessKeyId=LTAI5tCHjw6GESD5xjbRwYPN&Signature=oLFS%2B7lSsJ8G4pLiP0CY%2BZWttXI%3D 2025-09-02 18:28:41 丁贵清 我刚重启了一下，卡在停止中有好几分钟了 2025-09-02 18:28:48 丁贵清 https://yuntai-app-data.oss-cn-shanghai.aliyuncs.com/13000c3b-f9c2-479c-9e1b-3719a859c43a-20250902182849-877.png?Expires=4133952000&OSSAccessKeyId=LTAI5tCHjw6GESD5xjbRwYPN&Signature=%2BZ4ZueDkrT%2FYP39BD061pDbpO6A%3D 2025-09-02 18:33:32 峪森 会先走内部reboot，内部reboot耗时长了 2025-09-02 18:34:04 丁贵清 那这个要怎么解决呢 2025-09-02 18:34:48 峪森 你在机器内reboot看下呢，看看是什么服务卡住了 2025-09-02 18:35:38 峪森 我启动的时候发现有很多服务在做配置 2025-09-02 18:37:46 丁贵清 我那个密码登不进去了，你当时是用那个密码登录的吗？ 2025-09-02 18:39:24 峪森 没有 2025-09-02 18:39:31 峪森 报密码错误 2025-09-02 18:39:43 丁贵清 好的，明天让运维的再试下 2025-09-02 18:39:52 峪森 启动的时候我看到有个自动修改密码的服务 2025-09-02 18:40:04 峪森 你们应该自动改密码了 2025-09-02 18:40:21 丁贵清 等明天的时候让他那边远程登录试一下吧 2025-09-02 18:40:23 丁贵清 谢谢 2025-09-02 18:40:35 峪森 > ###### 丁贵清 > > 等明天的时候让他那边远程登录试一下吧 ------ #### 好的 2025-09-03 13:38:53 公共云TAM 您好，请问远程登录正常 了吗？ 2025-09-03 17:26:08 丁贵清 正常了 2025-09-03 17:29:14 峪森 好的，那工单就先结单了哈，有其他问题您再提单反馈我们处理~ 2025-09-03 17:29:30 丁贵清 好的，麻烦了 2025-09-03 17:30:31 峪森 > ###### 丁贵清 >好的，麻烦了 --------------- #### [抱拳] 2025-09-03 17:31:44 阿里云服务台 【问题描述】ECS实例i-2zej47d699e60ikj68vv操作系统崩溃并重启，导致系统无法正常启动。 【原因分析】操作系统内核panic导致系统重启，具体原因需要结合故障时收集到的vmcore进一步分析。挂载分区存在问题，且grub引导出现问题。 【解决方案】1、建议配置kdump以及hungtask_panic以收集vmcore文件进行进一步分析。 2.无法启动为grub中指定的分区错误导致，已修复。 2025-09-03 17:31:44 公共云TAM 【问题描述】ECS实例i-2zej47d699e60ikj68vv操作系统崩溃并重启，导致系统无法正常启动。 【原因分析】操作系统内核panic导致系统重启，具体原因需要结合故障时收集到的vmcore进一步分析。挂载分区存在问题，且grub引导出现问题。 【解决方案】1、建议配置kdump以及hungtask_panic以收集vmcore文件进行进一步分析。 2.无法启动为grub中指定的分区错误导致，已修复。
    """
    # 实例出现操作系统崩溃并重启，无法开启Kdump服务
    test_question1 = """
    i-2ze7wtmioz39grs4oecd 问题：实例在出现了操作系统崩溃，导致实例被重启，目前在系统内无法开启Kdump服务， 已经授权，需要操作开启Kdump方便后期定位原因 2025-09-04 09:06:13 客户 i-2ze7wtmioz39grs4oecd 问题:实例在出现了操作系统崩溃,导致实例被重启,目前在系统内无法开启Kdump服务, 已经授权,需要操作开启Kdump方便后期定位原因 2025-09-04 09:22:39 服务小二 已收到您提交的问题 2025-09-04 09:28:50 服务小二 您好:您反馈的问题正在为您核实中,请不要关闭工单,处理后将会回复您。 2025-09-04 10:08:51 服务小二 您好,1、 经核实服务器上无法开启转储日志的原因是没有预留crashkernel导致的,这边手动设置了crashkernel,请业务低峰期做好最新的快照备份,在重启服务器观察下转储日志是否可以开启 2、对于宕机的情况,查看日志有内核调用 __list_add_valid+ 函数出错的信息,怀疑是内核问题导致的宕机 查看目前服务器启动的是三方内核,建议降级到centos官方内核运行观察一下,谢谢 2025-09-04 10:10:56 客户 不能升级高版本的内核吗? 2025-09-04 10:17:25 服务小二 您好, 可以升级高版本内核的,但是查看服务器上配置的非centos官方内核,建议使用centos官方内核比较稳定,谢谢 2025-09-04 10:40:41 客户 有官方内核的链接? 2025-09-04 10:55:42 服务小二 您好, 经核实下centos7官方应该是 3.10.0-1160.119.1 这个版本:https://**/centos/7.9.2009/updates/x86_64/Packages/kernel-3.10.0-1160.119.1.el7.x86_64.rpm, 如果要更高的,建议升级服务器到alinux3操作系统,谢谢 2025-09-04 10:57:14 客户 好的
    """
    # 多个实例进行诊断问题
    test_question2 = """
        阿里云实例因实例错误实例重启 2025-09-03 10:36:58 阿里云服务台 ![screenshot](https://img.alicdn.com/imgextra/i3/O1CN017B6UP91aJZE5o3eg0_!!6000000003309-2-tps-1440-216.png) #### @谢依桐 您好，这里将为您解决您刚在（网易互娱企业级服务）提出的[E250903F242MHF](dingtalk://dingtalkclient/page/link?pc_slide=true&url=https%3A%2F%2Fm-yunqi.gts.work%2Fyunqi-customer-h5%2Ftask%2Furge%3Ftid%3D3624380%26departmentId%3D583646874%26snapshot%3D%26language%3D%26openconversitionid%3DcidUOz4w%252FpofdpeDW5tpsrD4g%253D%253D)企业工单，当前值班小二是[@尹立强]，您描述的问题为（[阿里云实例因实例错误实例重启](dingtalk://dingtalkclient/page/link?pc_slide=true&url=https%3A%2F%2Fm-yunqi.gts.work%2Fyunqi-customer-h5%2Ftask%2Furge%3Ftid%3D3624380%26departmentId%3D583646874%26snapshot%3D%26language%3D%26openconversitionid%3DcidUOz4w%252FpofdpeDW5tpsrD4g%253D%253D)）。 2025-09-03 10:36:58 阿里云服务台 ### 聊天记录 (2025-09-03 10:36:54) --- 阿里云服务台(2025-09-03 10:36:57): @谢依桐 已为您创建新企业工单 企业工单标题：阿里云实例因实例错误实例重启 企业工单ID：E250903F242MHF 企业工单状态：处理中 当前小二：尹立强 请[点击此处](dingtalk://dingtalkclient/page/link?pc_slide=true&url=https%3A%2F%2Fm-yunqi.gts.work%2Fyunqi-customer-h5%2Fgroup%2FjoinSubgroup%3FtaskId%3D3624380%26departmentId%3D583646874%26language%3D%26openconversitionid%3DcidwIByieMDOl%252FxIwLEWZcixQ%253D%253D) 进入企业工单专项群。 2025-09-03 10:37:54 谢依桐 i-wz9gcx7u64wghn8ny1z9 i-wz93yk96spgcbocopcji i-wz93hn7jm1k1ei4psi55 i-wz9hgan2bzgxtjz7jo86 i-wz985ddhar8npo7iz980 i-wz92800vffo9601pkak9 帮确认一下这6台实例2025年9月2日 21:26:05之前底层硬件和宿主的状况是否正常 2025-09-03 10:38:30 尹立强 收到，这边看下 2025-09-03 10:39:16 谢依桐 > ###### 尹立强 >收到，这边看下 --------------- #### [OK] 2025-09-03 10:45:31 尹立强 > ###### 谢依桐 > i-wz9gcx7u64wghn8ny1z9 > >i-wz93yk96spgcbocopcji > >i-wz93hn7jm1k1ei4psi55 > >i-wz9hgan2bzgxtjz7jo86 > >i-wz985ddhar8npo7iz980 > >i-wz92800vffo9601pkak9 > >帮确认一下这6台实例2025年9月2日 21:26:05之前底层硬件和宿主的状况是否正常 --------------- #### @谢依桐 这边看了下，这6个ecs实例对应的底层硬件和宿主机均无异常，看ecs实例在该时间点前都是出现了系统内部oom的情况 2025-09-03 10:48:47 谢依桐 > ###### 尹立强 > >#### @谢依桐 这边看了下，这6个ecs实例对应的底层硬件和宿主机均无异常，看ecs实例在该时间点前都是出现了系统内部oom的情况 --------------- #### [OK] 2025-09-03 11:15:43 谢依桐 这个事件查看的地方是有什么时效性的吗？ 2025-09-03 11:16:07 谢依桐 ![d8dd92c2-2871-4037-9060-7418c124f92d-20250903111607-936.png](https://yuntai-app-data.oss-cn-shanghai.aliyuncs.com/d8dd92c2-2871-4037-9060-7418c124f92d-20250903111607-936.png?Expires=4133952000&OSSAccessKeyId=LTAI5tCHjw6GESD5xjbRwYPN&Signature=Ol7ChBaevNBQDXr4FQCwVlS9%2FTg%3D) 刷新一下居然没有了 2025-09-03 11:16:38 谢依桐 ![604e69f7-e0eb-4d28-9d95-081715e798da-20250903111640-948.png](https://yuntai-app-data.oss-cn-shanghai.aliyuncs.com/604e69f7-e0eb-4d28-9d95-081715e798da-20250903111640-948.png?Expires=4133952000&OSSAccessKeyId=LTAI5tCHjw6GESD5xjbRwYPN&Signature=oMHxo4VCfjVxjA54mOe1W5ESB2w%3D) 我前面有截图，长这样的 2025-09-03 11:17:09 尹立强 @谢依桐 看这6台实例是都已经释放掉了 2025-09-03 11:17:27 谢依桐 释放了的话，这里就不会再有记录了对吧？ 2025-09-03 11:17:41 尹立强 > ###### 谢依桐 > 释放了的话，这里就不会再有记录了对吧？ --------------- #### @谢依桐 是的哈 2025-09-03 11:17:43 谢依桐 > ###### 尹立强 > >#### @谢依桐 是的哈 --------------- #### [OK] 2025-09-03 11:18:55 尹立强 @谢依桐 辛苦看下还有其他需要协助的不？ 2025-09-03 11:19:07 谢依桐 暂无，谢谢 2025-09-03 11:19:14 尹立强 > ###### 谢依桐 >暂无，谢谢 --------------- #### [送花花] 2025-09-03 11:19:34 尹立强 客气了哈，那这边就先待确认了，辛苦稍后点下结单按钮并进行评价哈[抱拳][送花花] 2025-09-03 11:21:11 尹立强 【问题描述】ecs实例底层是否有异常 【原因分析】6个ecs实例对应的底层硬件和宿主机均无异常，看ecs实例在该时间点前都是出现了系统内部oom的情况 2025-09-03 11:21:11 阿里云服务台 【问题描述】ecs实例底层是否有异常 【原因分析】6个ecs实例对应的底层硬件和宿主机均无异常，看ecs实例在该时间点前都是出现了系统内部oom的情况
    """
    # 分析宿主机CPU故障原因及G8A架构CPU高故障率问题
    test_question3 = """
        请在【2025-09-05 14:17:30】前进行评论响应，在【2025-09-06 01:17:30】前进行解决，超时未处理，会触发向上升级通知。云台升级单处理地址：https://yuntainext.aliyun-inc.com/order/my/detail?ticketId=452428 【紧急度】普通 【客户名称】北京欧凯联创顶级服务 【提单UID】1559032671635936 【业务影响】产品咨询 【是否有企业支持钉钉群】有 【产品名称】神龙数据稳定性值班（nc值班） 【升级原因】咨询问题 【工单地址】https://ticket.aliyun-inc.com/yunqi-workbench/page/yunqi-workbench?corpId=ding14b15b56be15e048&tenantCode=YUNQI_ETS&eidOrTaskId=3632090 【工单ID】3632090 【问题描述】 问题描述：核实实例宕机的具体原因 实例信息： i-j6c7kmpd1a24gtsi4vxp i-j6c9eeg2o773ludynr25 时间点：2025-09-04 21:09 处理进展：从后台来看是宿主机cpu故障导致的，但是g8a架构用的是不同的cpu，故障率太高了，不符合预期
    """
    # 实例未知原因重启需要诊断
    test_question4 = """
        问下 我有一台服务器 怎么莫名其妙重启了 能帮忙查下原因吗 2025-09-04 09:10:45 客户 问下 我有一台服务器 怎么莫名其妙重启了 能帮忙查下原因吗 2025-09-04 09:11:26 服务小二 您好,目前已进入在线人工服务,售后工程师将尽快为您处理,请稍候。 2025-09-04 09:11:43 客户 问下 我有一台服务器 怎么莫名其妙重启了 能帮忙查下原因吗 刚给我推送的短信 2025-09-04 09:11:48 服务小二 您好,麻烦您提供下实例id/公网ip。谢谢。 2025-09-04 09:12:08 客户 i-bp15fh424r0xa22kyva4 没有公网ip 2025-09-04 09:12:50 服务小二 您好:您的云服务器(实例ID:i-bp15fh424r0xa22kyva4,实例**_git,私网IP:["*************"]在北京时间 2025-09-04T09:08:03 因底层宿主机出现非预期的软硬件故障而宕机,重启的。 2025-09-04 09:13:06 客户 ??? 2025-09-04 09:13:15 客户 这个是怎么回事 2025-09-04 09:13:39 服务小二 您好,这台服务器重启,是因为底层宿主机出现非预期的软硬件故障而宕机,重启的。 2025-09-04 09:14:52 客户 这个风险怎么避免 2025-09-04 09:17:48 服务小二 您好,您可以理解这个是突发性的,不是人为去控制的,重启过程中,我们会将该实例迁移到其他健康的宿主机上。 2025-09-04 09:29:46 服务小二 您好,请问您是否还有其他问题咨询 2025-09-04 09:34:38 客户 行 2025-09-04 09:35:53 服务小二 嗯呢,有其他问题您及时反馈哈,这边协助您查看处理 2025-09-04 09:40:22 服务小二 您好,在线服务还请保持实时交互,长时间没回复,会话将会被系统自动关闭,谢谢 2025-09-04 09:45:30 服务小二 您可能在忙,所以一直没回复。在线服务无法长时间保持,长时间占线会导致其他用户无法接入,若您的问题还需继续处理可以点击下方“唤回工程师”,如您遇到其他问题,可选择提交新问题。
    """
    # 诊断实例因云盘BPS超限导致的连接问题
    test_question5 = """
        ecs问题排查 2025-09-02 16:38:56 阿里云服务台 ![screenshot](https://img.alicdn.com/imgextra/i3/O1CN017B6UP91aJZE5o3eg0_!!6000000003309-2-tps-1440-216.png) #### @Victor 您好，这里将为您解决您刚在（洋钱罐顶级服务）提出的[E250902B73P7W3](dingtalk://dingtalkclient/page/link?pc_slide=true&url=https%3A%2F%2Fm-yunqi.gts.work%2Fyunqi-customer-h5%2Ftask%2Furge%3Ftid%3D3622719%26departmentId%3D66589057%26snapshot%3D%26language%3D%26openconversitionid%3DcideQc9gHfudiCG%252BJpq0ICTeg%253D%253D)企业工单，当前值班小二是[@煊童]，您描述的问题为（[ecs问题排查](dingtalk://dingtalkclient/page/link?pc_slide=true&url=https%3A%2F%2Fm-yunqi.gts.work%2Fyunqi-customer-h5%2Ftask%2Furge%3Ftid%3D3622719%26departmentId%3D66589057%26snapshot%3D%26language%3D%26openconversitionid%3DcideQc9gHfudiCG%252BJpq0ICTeg%253D%253D)）。 2025-09-02 16:38:57 阿里云服务台 ### 聊天记录 (2025-09-02 16:38:53) --- Victor(2025-09-02 16:38:51): @阿里云服务台 ecs问题排查 --- 阿里云服务台(2025-09-02 16:38:56): @Victor 已为您创建新企业工单 企业工单标题：ecs问题排查 企业工单ID：E250902B73P7W3 企业工单状态：处理中 当前小二：煊童 请[点击此处](dingtalk://dingtalkclient/page/link?pc_slide=true&url=https%3A%2F%2Fm-yunqi.gts.work%2Fyunqi-customer-h5%2Fgroup%2FjoinSubgroup%3FtaskId%3D3622719%26departmentId%3D66589057%26language%3D%26openconversitionid%3DcidomHk9juhWAi462Pi%252BMPD9Q%253D%253D) 进入企业工单专项群。 2025-09-02 16:39:08 煊童 您好，请问有什么可以帮到您？ 2025-09-02 16:39:15 Victor 有台节点今天9点20-到10点：30 都连不上，帮忙排查下是什么问题 emr-user@gateway-2-8(172.20.143.44) 2025-09-02 16:39:34 煊童 您好，辛苦提供下实例ID 2025-09-02 16:39:42 Victor https://yuntai-app-data.oss-cn-shanghai.aliyuncs.com/f6cb00b8-d2c2-43f1-8044-18cffb12f734-20250902163944-183.png?Expires=4133952000&OSSAccessKeyId=LTAI5tCHjw6GESD5xjbRwYPN&Signature=gU3H7A7OBl0mrwh1atc4wXKdGvo%3D 2025-09-02 16:40:10 煊童 @Victor 实例ID发下哈 2025-09-02 16:42:30 Victor 我查下 2025-09-02 16:42:48 煊童 好的 2025-09-02 16:47:05 Victor i-2ze1q3tkgd3gtxiccnl6 2025-09-02 16:47:18 煊童 好的 2025-09-02 16:48:36 Victor https://yuntai-app-data.oss-cn-shanghai.aliyuncs.com/58a9a078-31f1-4fb1-bbce-fa5e8437e6ca-20250902164836-470.png?Expires=4133952000&OSSAccessKeyId=LTAI5tCHjw6GESD5xjbRwYPN&Signature=HReZhTHYkpiA%2FoO3Jmo5huh4Sco%3D 2025-09-02 16:50:50 煊童 @Victor 您好，我看了下，早上的时候云盘超限了 2025-09-02 16:51:53 煊童 两块盘的bps都超了 2025-09-02 16:52:32 Victor 能查是哪个程序超了么？或者下次超的时候，如何能让我登录上or记录下 2025-09-02 16:55:50 煊童 > ###### Victor > 能查是哪个程序超了么？或者下次超的时候，如何能让我登录上or记录下 --------------- #### @Victor 当时有试过vnc登陆吗？ 2025-09-02 16:56:47 Victor 当时是ssh登录的 2025-09-02 16:57:41 煊童 > ###### Victor > 当时是ssh登录的 --------------- #### @Victor 您后续如果再次遇到的话可以尝试通过VNC登陆看下，另外也可以通过云监控的进程监控来侧面分析下 2025-09-03 10:08:16 煊童 @Victor 您好，请问针对该问题还有其它我们能够协助您的吗？ 2025-09-03 10:57:03 煊童 @Victor 您好，请问针对该问题还有其它我们能够协助您的吗？ 2025-09-04 17:32:15 煊童 @Victor 您好，鉴于您长时间未反馈，为避免频繁打扰您，当前工单这边就暂时置为待确认了，工单保留7天，您这边后期有问题可以重新提交工单反馈。 2025-09-04 17:32:42 煊童 【问题描述】一台ecs节点在9点20到10点30之间无法连接，实例ID为i-2ze1q3tkgd3gtxiccnl6，原因是云盘bps超限。 【原因分析】两块盘的bps都超限。 【解决方案】1、后续如果再次遇到类似问题，可以通过VNC登陆查看 2、也可以通过云监控的进程监控来侧面分析问题 2025-09-04 17:32:42 阿里云服务台 【问题描述】一台ecs节点在9点20到10点30之间无法连接，实例ID为i-2ze1q3tkgd3gtxiccnl6，原因是云盘bps超限。 【原因分析】两块盘的bps都超限。 【解决方案】1、后续如果再次遇到类似问题，可以通过VNC登陆查看 2、也可以通过云监控的进程监控来侧面分析问题
    """
    # 诊断实例频繁死机问题
    test_question6 = """
        人工服务。 2025-09-03 21:09:23 客户 人工服务。 2025-09-03 21:09:56 客户 ECS频繁死机 2025-09-03 21:10:00 服务小二 您好,目前已进入在线人工服务,售后工程师将尽快为您处理,请稍候。 2025-09-03 21:10:24 服务小二 您好,请您提供下服务器的实例id/IP地址,谢谢。 2025-09-03 21:10:28 客户 i-25w7oxyh9 2025-09-03 21:10:47 客户 这台服务器今天晚上已经死机3次了。 2025-09-03 21:11:04 客户 看下这个工单:000BSK4YUW 2025-09-03 21:11:20 客户 我已经联系你们2次了。 2025-09-03 21:13:07 服务小二 您好,您的这个工单:000BSK4YUW 中的工程师,正在帮您处理 看您之前是给对应工程师有授权服务器。麻烦您稍等,这边帮您联系对应的工程师加急帮您再看一下 2025-09-03 21:13:36 客户 马上,太着急了,已经影响我们业务了。 2025-09-03 21:20:01 服务小二 当前后台工程师正在加急帮您处理。 您后续关注工单:000BSK4YUW 工程师有结果会第一时间给您反馈 2025-09-03 21:29:17 服务小二 您好,后续麻烦您关注工单:000BSK4YUW 当前无其它问题的话。稍后我将结束本次服务;后期如有问题可随时联系我们,我们将竭诚为您服务。
    """
    # 分析三台实例在CPU利用率和内存使用率不高的情况下多次宕机的原因。
    test_question7 = """
        我的三台服务器组cpu利用率和内存不高的情况下，宕机了是什么情况 2025-09-02 18:07:12 客户 我的三台服务器组cpu利用率和内存不高的情况下,宕机了是什么情况 2025-09-02 18:08:10 客户 我的三台服务器组cpu利用率和内存不高的情况下,宕机了是什么情况 2025-09-02 18:08:15 服务小二 您好,目前已进入在线人工服务,售后工程师将尽快为您处理,请稍候。 2025-09-02 18:08:28 服务小二 您好,已经收到您的反馈,正在为您查看中。请耐心等待。谢谢。 2025-09-02 18:08:37 服务小二 您分别反馈下实例ID信息 2025-09-02 18:08:47 客户 你好我alb服务器后面又3台服务器,利用率都不高,单机好几次了 2025-09-02 18:09:13 客户 alb实例id:alb-7gbpava50y8j62sdjz 2025-09-02 18:09:48 客户 i-2ze7eyqs7x9h2hi10yl7 i-2zegufn86w2t79metyhi i-2zedyd31r2cactynk6v3 2025-09-02 18:10:06 客户 这是后端服务器,宕机这是这3台 2025-09-02 18:10:13 服务小二 好的,稍后 2025-09-02 18:11:33 服务小二 查询看近期这三个实例没有宕机的记录,您是业务有出现什么异常么 2025-09-02 18:13:21 客户 我刚才重启了服务器 2025-09-02 18:13:30 客户 我ssh连接失败 2025-09-02 18:13:49 服务小二 现在可以连接么 2025-09-02 18:13:51 客户 附件 2025-09-02 18:14:02 客户 重启之后,过了3分钟,又连不上了 2025-09-02 18:14:22 服务小二 现在可以复现问题么 2025-09-02 18:14:32 客户 附件 2025-09-02 18:14:40 客户 是的,现在也连不上 2025-09-02 18:14:48 客户 这利用率并不高呀 2025-09-02 18:15:10 服务小二 您可以任意提供下其中一台实例的授权,这边登录检查看看 2025-09-02 18:16:18 客户 已操作授权 2025-09-02 18:25:44 服务小二 您好,出现问题前有做过什么特殊操作没 2025-09-02 18:26:03 客户 没做过呀 2025-09-02 18:26:21 客户 安全组操作也没有做 2025-09-02 18:26:34 客户 而且重启也能连上一阵 2025-09-02 18:26:54 客户 我看后台也是正常 2025-09-02 18:27:08 客户 请求也不超时 2025-09-02 18:28:46 服务小二 根据日志信息来看是提示这个模块无法加载,您看方便可以给这个服务器做一个最新的快照备份么,这边尝试修复 2025-09-02 18:30:53 客户 好 2025-09-02 18:32:27 服务小二 OK 看到您已经创建了,该问题修复方法较为复杂,这边帮您把问题转交下后端专员协助您进行处理及跟进下 2025-09-02 18:33:00 服务小二 这边帮您转接工单核实一下是否可以恢复,请您稍等。 您好,您的问题需要通过工单进一步排查处理。稍后我会将当前对话转交工单处理,无需您新建工单,届时您可以通过新创建的工单号继续查看问题处理。 提醒:工单排查需要一定时间,不是实时回复,届时您可以在控制台-工单-我的工单中查看进度并进行回复,辛苦耐心等待。 2025-09-02 18:33:24 客户 好的
    """
    test_question8 = """
        服务器自动被重启了是什么原因？ 2025-09-05 16:02:17 客户 服务器自动被重启了是什么原因? 2025-09-05 16:04:57 服务小二 已收到您提交的问题 2025-09-05 16:08:51 服务小二 您好,抱歉给您带来的不好体验。 您的云服务器(实例ID:i-bp1ik7e29fvo2v1io822,在北京时间 ** 因底层宿主机出现非预期的软硬件故障而宕机,阿里云对该实例进行重启恢复操作。 目前物理机硬件故障无法100%避免,阿里云也在不断通过各种技术手段主动规避和降低此类风险,但无法完全避免这种问题发生。当出现ECS所在物理机无法正常对外提供服务时,系统会自动触发保护性宕机迁移,为了降低您的业务受到此类事件的影响,建议您通过多种方式提高业务容错率以降低宕机迁移操作带来的影响。例如: 1、将您的核心应用程序(服务)添加到自启动项列表中,避免业务中断。 2、开启应用程序的自动重新连接功能。例如,允许应用程序自动连接到MySQL、SQL Server或Apache Tomcat。 3、如果您同时使用了负载均衡服务,请将多台ECS实例部署在集群环境中,当某一台ECS实例处于自动恢复过程中时,其余ECS实例可以继提供业务访问能力。 4、定期备份本地盘上的数据,以实现数据冗余和提供实例重新部署的数据文件。 2025-09-05 16:10:11 客户 这阵子被搞了2台服务器,以前没遇到过这种严重的问题 2025-09-05 16:10:43 服务小二 十分抱歉,目前物理机硬件故障无法100%避免,阿里云也在不断通过各种技术手段主动规避和降低此类风险,但无法完全避免这种问题发生。 2025-09-05 16:10:56 客户 最近是出什么问题了吗,这么频繁出现这么严重的故障 2025-09-05 16:11:42 客户 接连被搞了2台,这是啥概率? 2025-09-05 16:12:05 客户 自认倒霉?? 2025-09-05 16:12:50 客户 轻描淡写发个短信就完了? 2025-09-05 16:13:56 客户 这以后还能用不?晕死 2025-09-05 16:14:10 服务小二 您好,您的问题已经转交相关专员稍后继续处理,请您暂时不要关闭工单,等待我们的进一步答复。 2025-09-05 16:16:28 客户 第一台被重启了也没说什么,偶尔谁都能理解,但前天又被整了一台,后面是不是接二连三的出问题,真怕了,到底什么原因接二连三的必须要有个解释,哪有这样的故障和处理态度 2025-09-05 16:44:19 服务小二 确实抱歉给您带来不好的感受, 目前ecs服务器的话, 可用性并非100% 的。 对于可用性说明, 可以参考: https://**/legal-agreement/terms/suit_bu1_ali_cloud/suit_bu1_ali_cloud2**_62160.html 详细了解看下。 (1)对于单实例维度, 阿里云承诺一个服务周期内ECS的服务可用性不低于99.975%; (2)对于单地域多可用区维度,阿里云承诺一个服务周期内ECS的服务可用性不低于99.995%。 对于您反馈的近期出现两次重启的情况, 另一台发生重启的ecs ID以及重启时间您可以提供下, 这边帮您查询看看是否都是同一个原因。 另外,对于ecs 底层宿主机这边也是在不断进行优化的, 尽量去避免类似问题出现,但是这个确实无法100% 避免的,确实抱歉给您带来不好的体验了 。对于承载重要业务的服务器,建议您从业务层面做下冗余,例如结合业务侧评估看看能否接入负载均衡, 尽量避免类似单点故障的问题。
    """
    # 用户返回自己的64台服务器有4台已出现问题，要求扫描全量
    test_question9 = """
            ecs硬件问题 2025-09-05 17:16:09 阿里云服务台 ### 聊天记录 (2025-09-05 17:16:07) --- 阿里云服务台(2025-09-05 17:16:09): @徐文强 已为您创建新企业工单 企业工单标题：ecs硬件问题 企业工单ID：E250905GN41WUA 企业工单状态：处理中 当前小二：徐文强 请[点击此处](dingtalk://dingtalkclient/page/link?pc_slide=true&url=https%3A%2F%2Fm-yunqi.gts.work%2Fyunqi-customer-h5%2Fgroup%2FjoinSubgroup%3FtaskId%3D3635486%26departmentId%3D998936232%26language%3D%26openconversitionid%3DcidtHzogFzHZjmG3fWSdY2wFg%253D%253D) 进入企业工单专项群。 2025-09-05 17:16:09 阿里云服务台 ![screenshot](https://img.alicdn.com/imgextra/i3/O1CN017B6UP91aJZE5o3eg0_!!6000000003309-2-tps-1440-216.png) #### @徐文强 您好，这里将为您解决您刚在（厦门雷霆互动企业基础服务支持）提出的[E250905GN41WUA](dingtalk://dingtalkclient/page/link?pc_slide=true&url=https%3A%2F%2Fm-yunqi.gts.work%2Fyunqi-customer-h5%2Ftask%2Furge%3Ftid%3D3635486%26departmentId%3D998936232%26snapshot%3D%26language%3D%26openconversitionid%3DcidUKfUrNq9Wva9cuRxMhPC3A%253D%253D)企业工单，当前值班小二是[@徐文强]，您描述的问题为（[ecs硬件问题](dingtalk://dingtalkclient/page/link?pc_slide=true&url=https%3A%2F%2Fm-yunqi.gts.work%2Fyunqi-customer-h5%2Ftask%2Furge%3Ftid%3D3635486%26departmentId%3D998936232%26snapshot%3D%26language%3D%26openconversitionid%3DcidUKfUrNq9Wva9cuRxMhPC3A%253D%253D)）。 紧急处理：如问题造成您的业务受损，需要紧急处理，请点击[[紧急处理](dingtalk://dingtalkclient/page/link?pc_slide=true&url=https%3A%2F%2Fm-yunqi.gts.work%2Fyunqi-customer-h5%2Ftask%2FquickUrge%3Ftid%3D3635486%26departmentId%3D998936232%26snapshot%3D%26language%3D%26judgeType%3D1%26openconversitionid%3DcidUKfUrNq9Wva9cuRxMhPC3A%253D%253D)]按钮，我们以最高优先级紧急处理。 2025-09-05 17:18:47 徐文强 雷霆近期 多例ecs有宕机情况，客户反馈需要内部进行风险扫描，实例列表： i-uf6hyrdxb6a1atcu4qem i-uf6ckt39uik0l7zyu02u i-uf6h9ntcldpxl189op11 i-uf65gx2sujlbwjc53dpx i-uf6clbjoqbug90vnt38l i-uf6ff64hrnolstqo32si i-uf62rg56y1plknhcihyh i-uf675t0pmlgkl5tscmas i-uf66ayl62x2anpy1xcai i-uf63hampcn73hsplw4ho i-uf62ze7lo6fcs6w7t7z1 i-uf64rfnww427arfapn0r i-uf64447wzbkbd0yxnzgx i-uf6jfb04ar5gmbzvnh8t i-uf6b0qh1v24gdqzne5m5 i-uf6imatkod4hhtyqbhfy i-uf6ckt39uik0l7zyu035 i-uf60w2q6v76o5e0e9stg i-uf68tng6407fe3gft8q1 i-uf6b11p0ocsoei7x6iga i-uf6cgorzlesp4zzvt06r i-uf69hwghukbnoi36k9gs i-uf68tng6407fe3gft8q2 i-uf68bzop1nw2srczvvi1 i-uf62mm8g4ek01lum9few i-uf6960p57rjxrlhr7ff4 i-uf6h2n0gd38sk1rtzpn5 i-uf6e551eps3zyxnbbw58 i-uf64jdy6yxmi5elaefrp i-uf63kpv8eqm3s05082pj i-uf65ufupg0du8s6fusez i-uf6e2rdomckmq7oso7hq i-uf63nboo2jfgtl2vn40d i-uf6ad5lqmfp36vkzlusb i-uf682vs2c7693355bkek i-uf68stghx92iwztc1j6e i-uf68ac9xenk1ukiypjh4 i-uf6gzbx5of0ll9frkew3 i-uf6clbjoqbug90vnt38m i-uf66rtog2qp201ff5qv0 i-uf63nboo2jfgtl2vn40e i-uf63kpv8eqm3s05082pk i-uf6ima1w4slncpa4u0x9 i-uf6clbjoqbug90vnt38n i-uf6ew1z45chfp5n7eh6q i-uf6fk3v6h5bgn156wglq i-uf62fwvw2f8e18a5oufs i-uf6c3p4h2vaebewdacpf i-uf6cf5eqxw02q083ah1b i-uf6c5yqo9gfycctz63iu i-uf6ima1w4slncpa4u0xa i-uf63nboo2jfgtl2vn40g i-uf6hyrdxb6a1buuf9wht i-uf69tdzbp5etlmavgcyx i-uf6ima1w4slncpa4u0xb i-uf65gx2sujlbxktq8jso i-uf6e2rdomckmq7oso7hs i-uf668lw8f0hllmgr66cs i-uf6cf5eqxw02q083ah1c i-uf61lfiz0wrulj5ypu3m i-uf64rfnww427arfapn3l i-uf61lfiz0wrulj5ypu3l i-uf68tng6407fe3gft8rh i-uf65gx2sujlbxktq8jsp 2025-09-05 17:18:47 徐文强 https://gtsp-bap-center.oss-cn-beijing.aliyuncs.com/93c8f8efb0b14ba9b9308f9ebac7e2dfimage.png 2025-09-05 17:19:13 徐文强 https://gtsp-bap-center.oss-cn-beijing.aliyuncs.com/363600f9efa143c9929f7c0a6e912410image.png 2025-09-05 17:19:13 徐文强 同期 64 个机器，1 个已经故障重启，3 个待故障重启，6.25% 概率了 ，都再检查下吧 2025-09-05 17:19:49 徐文强 通过巡检uid 下的所有ECS资源，针对未达到线上主动运维标准的、预估可能潜存在的风险，在整体低风险的实例中识别出风险相对较高的实例，我们可以协助其进行尝试热迁移，达到进一步抑制非预期宕机率效果。 这里我也做下说明： 触发主动运维事件的实例是存在一定风险的， 针对这些未产生主动运维事件的实例，说明风险未达到线上触发自动运维事件的阈值，我们这里是进行预防性保障措施，治病于未病，杜绝潜在稳定性隐患，所有避免您有误解这里跟您也说明一下 下面这些实例看都是大规格，热迁可能会失败，建议运维窗口期控制台重启下漂移 i-uf63oijuz1edcpulh64g i-uf6hyrdxb6a1buuf9wht i-uf68ukikpyw9o5m9zjoo i-2ze5po90og5fafm6egnk（尝试无损热迁） 1台可以无损热迁的我们先在就可以尝试迁移，几乎对业务无感的，您看下要迁吗，其余的如果是业务在用的实例，要您在业务运维窗口期控制台重启下即可 2025-09-05 17:21:41 徐文强 后续作为客户稳定性主动优化项每周底层硬件巡检，客户每周四为运维窗口期，需要周3前反馈存在风险隐患实例到客户侧，后续将按计划巡检宿主机 2025-09-08 09:39:43 阿里云服务台 【问题描述】多例ecs出现宕机情况，客户反馈需要内部进行风险扫描。 【解决方案】1、针对未达到线上主动运维标准的、预估可能潜存在的风险实例，进行预防性保障措施。 2、对于大规格实例，建议在运维窗口期内控制台重启。 3、对于可以无损热迁的实例，进行无损热迁。 4、后续每周四为运维窗口期，客户需在周三前反馈存在风险隐患的实例。 2025-09-08 09:39:43 徐文强 【问题描述】多例ecs出现宕机情况，客户反馈需要内部进行风险扫描。 【解决方案】1、针对未达到线上主动运维标准的、预估可能潜存在的风险实例，进行预防性保障措施。 2、对于大规格实例，建议在运维窗口期内控制台重启。 3、对于可以无损热迁的实例，进行无损热迁。 4、后续每周四为运维窗口期，客户需在周三前反馈存在风险隐患的实例。
        """
    test_question10 = """
        通过 vmcore 诊断 NC 11.196.165.144 在时间 2025-09-07 13:39:59 的宕机原因
    """
    test_question11 = """
        通过 vmcore 诊断 NC 26.78.205.43 在时间 2025-09-10 05:13:41 的宕机原因
    """

    test_question12 = """
        通过 vmcore 诊断 NC 11.115.236.173 在时间 2025-09-09 19:13:01 的宕机原因
    """
    ##test_question ="查询用户aliUid 1781574661016173的最近2天健康报告"
    print(f"📝 测试问题: {test_question10}")
    
    # 测试 Chat V1 API
    print("🚀 开始测试 Chat V1 API...")
    try:
        result = await tester.test_chat_v1_sse_events(test_question10, save_to_file=True)
        
        # 验证基本结果
        assert result["total_events"] > 0, "应该收到事件"
        
        print(f"✅ 收到 {result['total_events']} 个事件")
        metrics = result.get("metrics", {})
        if metrics:
            if metrics.get("first_token_seconds") is not None:
                print(f"⏱️ First token: {metrics['first_token_seconds']:.3f}s")
            print(f"⏱️ SSE 总耗时: {metrics.get('stream_duration_seconds', 0.0):.3f}s")
            print(f"📦 SSE 总行数: {metrics.get('sse_lines', 0)}")
        
        # 验证 SSE 格式
        event_lines = [line for line in result["sse_events"] if line.startswith("event: ")]
        data_lines = [line for line in result["sse_events"] if line.startswith("data: ")]
        
        print(f"✅ event: 行数: {len(event_lines)}")
        print(f"✅ data: 行数: {len(data_lines)}")
        
        if result.get("file_path"):
            print(f"✅ 事件文件: {result['file_path']}")
        
        assert len(data_lines) > 0, "应该包含 data: 行"
        
        print("🎉 Chat V1 API SSE 事件测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")

@pytest.mark.asyncio
@pytest.mark.integration  
async def skip_test_chat_v1_api_sse_events_e2e_interactive_agent():
    """
    测试 InteractiveAgent 的 SSE 事件端到端测试（需要服务器运行）
    """
    print("\n" + "="*80)
    print("开始 InteractiveAgent SSE 事件端到端测试")
    print("="*80)
    
    # 初始化测试器
    tester = ChatV1APITester()
    
    print("🔐 正在进行身份认证...")
    try:
        token = await tester.authenticate()
        tester.token = token
        tester.headers["Authorization"] = f"Bearer {token}"
        print("✅ 身份认证成功")
    except Exception as e:
        pytest.skip(f"认证失败，跳过测试: {e}")
    
    # 准备测试数据
    test_question = "请分析这段日志，找出可能的异常原因。"
    
    print(f"📝 测试问题: {test_question}")
    
    # 测试 InteractiveAgent
    print("🚀 开始测试 InteractiveAgent SSE 事件...")
    try:
        result = await tester.test_interactive_agent_sse_events(test_question, additional_info={"context": "系统日志分析"})
        
        # 验证基本结果
        assert result["total_events"] > 0, "应该收到事件"
        
        print(f"✅ 收到 {result['total_events']} 个事件")
        metrics = result.get("metrics", {})
        if metrics:
            if metrics.get("first_token_seconds") is not None:
                print(f"⏱️ First token: {metrics['first_token_seconds']:.3f}s")
            print(f"⏱️ SSE 总耗时: {metrics.get('stream_duration_seconds', 0.0):.3f}s")
            print(f"📦 SSE 总行数: {metrics.get('sse_lines', 0)}")
        
        # 验证 SSE 格式
        event_lines = [line for line in result["sse_events"] if line.startswith("event: ")]
        data_lines = [line for line in result["sse_events"] if line.startswith("data: ")]
        
        print(f"✅ event: 行数: {len(event_lines)}")
        print(f"✅ data: 行数: {len(data_lines)}")
        
        assert len(data_lines) > 0, "应该包含 data: 行"
        
        print("🎉 InteractiveAgent SSE 事件测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_chat_v1_api_sse_events_e2e_inspect_agent():
    """
    测试 InspectAgent 的 SSE 事件端到端测试（需要服务器运行）
    """
    print("\n" + "="*80)
    print("开始 InspectAgent SSE 事件端到端测试")
    print("="*80)
    
    # 初始化测试器
    tester = ChatV1APITester()
    
    print("🔐 正在进行身份认证...")
    try:
        token = await tester.authenticate()
        tester.token = token
        tester.headers["Authorization"] = f"Bearer {token}"
        print("✅ 身份认证成功")
    except Exception as e:
        pytest.skip(f"认证失败，跳过测试: {e}")
    
    # 准备测试数据
    test_question = "请分析这段日志，找出可能的异常原因。"
    
    print(f"📝 测试问题: {test_question}")

    # 测试 InspectAgent
    print("🚀 开始测试 InspectAgent SSE 事件...")
    test_cases = [
        {
            "machine_id": "i-bp1fz27ong6p6w693vn5",
            "description": "ECS Instance ID 测试",
            "start_time": "2025-07-28 00:00:00",
            "end_time": "2025-07-30 23:59:59",
        },
        {
            "machine_id": "***********",
            "description": "NC IP Address 测试",
            "start_time": "2025-07-28 00:00:00",
            "end_time": "2025-07-30 23:59:59",
        },
        {
            "machine_id": "i-2zebte5jxlkv2jscbwjb",
            "description": "ECS Instance ID 测试",
            "start_time": "2025-07-28 00:00:00",
            "end_time": "2025-07-30 23:59:59",
        },
    ]

    try:
        result = await tester.test_inspect_agent_sse_events(test_question, additional_info=test_cases[0])

        # 验证基本结果
        assert result["total_events"] > 0, "应该收到事件"
        
        print(f"✅ 收到 {result['total_events']} 个事件")
        metrics = result.get("metrics", {})
        if metrics:
            if metrics.get("first_token_seconds") is not None:
                print(f"⏱️ First token: {metrics['first_token_seconds']:.3f}s")
            print(f"⏱️ SSE 总耗时: {metrics.get('stream_duration_seconds', 0.0):.3f}s")
            print(f"📦 SSE 总行数: {metrics.get('sse_lines', 0)}")
        
        # 验证 SSE 格式
        event_lines = [line for line in result["sse_events"] if line.startswith("event: ")]
        data_lines = [line for line in result["sse_events"] if line.startswith("data: ")]
        
        print(f"✅ event: 行数: {len(event_lines)}")
        print(f"✅ data: 行数: {len(data_lines)}")
        
        assert len(data_lines) > 0, "应该包含 data: 行"
        
        print("🎉 InteractiveAgent SSE 事件测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


if __name__ == "__main__":
    """
    直接运行测试
    
    运行方式：
    # 无需服务器的测试
    python tests/integration/api/test_chat_v1_integration.py
    
    # 或使用 pytest
    pytest tests/integration/api/test_chat_v1_integration.py -v -s

    pytest tests/integration/api/test_chat_v1_integration.py::test_chat_v1_api_sse_events_e2e_interactive_agent -v -s
    """

    # pytest.main([__file__, "-v", "-s", "--tb=short"])
    # 如果需要测试 InteractiveAgent，可以取消注释以下行
    pytest.main([__file__, "-v", "-s", "--tb=short", "--runxfail", "test_chat_v1_api_sse_events_e2e_interactive_agent"])
    print("测试完成！")
    