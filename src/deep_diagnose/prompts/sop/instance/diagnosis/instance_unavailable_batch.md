# SOP：批量实例不可用诊断方案

## 诊断步骤（强制遵循，不得增减）

### 步骤1：批量查询实例物理机分布情况
**目标**：分析批量故障实例的物理机分布模式，识别是否存在NC层面的聚集性故障
- **工具**：`listVmHostHistory`
- **输入参数**：
  - **instanceId**：批量实例ID列表（逐个调用或批量传入）
  - **startTime**：问题发生时间前1小时（格式：YYYY-MM-DD HH:MM:SS）
  - **endTime**：问题发生时间后1小时（格式：YYYY-MM-DD HH:MM:SS）
- **执行策略**：对每个实例ID分别调用工具，收集完整的物理机映射信息
- **分析要点**：
  - 统计涉及的唯一物理机数量
  - 计算实例在物理机上的分布密度
  - 识别是否存在多实例集中在少数NC的聚集性模式
  - 分析物理机故障对批量实例的影响范围
- **标准输出格式**：
  ```
  【物理机分布分析】
  - 涉及物理机总数：Y台NC
  - 聚集性评估：[高聚集/中聚集/分散分布]
  - 关键NC列表：
    * NC-IP-1: 影响实例[instance-1, instance-2, ...]（共Z个）
    * NC-IP-2: 影响实例[instance-3, instance-4, ...]（共W个）
  - 聚集性结论：[NC层面批量故障/实例层面独立故障]
  ```

### 步骤2：批量实例可用性根因诊断
**目标**：对每个故障实例进行深度诊断，识别共同故障模式和根本原因
- **工具**：`runDiagnose`
- **输入参数**：
  - **machineId**：单个实例ID（批量处理时逐个调用）
  - **startTime**：问题发生时间前2小时（格式：YYYY-MM-DD HH:MM:SS）
  - **endTime**：问题发生时间后1小时（格式：YYYY-MM-DD HH:MM:SS）
  - **type**：`REASON_VM_APPLICABILITY`（VM可用性问题诊断）
- **执行策略**：对每个故障实例分别进行诊断，然后进行批量分析
- **分析要点**：
  - 收集每个实例的具体不可用原因
  - 统计故障原因的分布模式
  - 识别共同的故障触发因素
  - 分析故障时间的集中性和关联性
- **标准输出格式**：
  ```
  【批量根因诊断结果】
  - 诊断实例总数：X个
  - 故障原因分类统计：
    * 硬件故障：Y个实例 [instance-1, instance-2, ...]
    * 网络异常：Z个实例 [instance-3, instance-4, ...]
    * 系统重启：W个实例 [instance-5, instance-6, ...]
    * 其他原因：V个实例 [instance-7, instance-8, ...]
  - 主要故障模式：[故障原因名称]（占比：XX%）
  - 故障时间分布：
    * 集中时间段：YYYY-MM-DD HH:MM:SS ~ YYYY-MM-DD HH:MM:SS
    * 时间跨度：X分钟
  - 批量故障结论：[同根因批量故障/多因素混合故障]
  ```

### 步骤3：批量变更记录关联分析
**目标**：检查批量实例相关的变更操作，识别可能导致批量故障的变更事件
- **工具**：`listChangeRecords`
- **输入参数**：
  - **machineIds**：批量实例ID列表（数组格式）
  - **startTime**：问题发生时间前4小时（格式：YYYY-MM-DD HH:MM:SS）
  - **endTime**：问题发生时间后1小时（格式：YYYY-MM-DD HH:MM:SS）
- **执行策略**：一次性查询所有实例的变更记录，进行批量关联分析
- **分析要点**：
  - 统计变更记录的总体分布
  - 识别高危变更操作类型和频次
  - 分析变更时间与故障时间的时序关联
  - 评估变更操作对批量实例的影响范围
  - 识别可能的变更级联影响
- **标准输出格式**：
  ```
  【批量变更记录分析】
  - 查询时间范围：YYYY-MM-DD HH:MM:SS ~ YYYY-MM-DD HH:MM:SS
  - 变更记录总数：X条
  - 高危变更类型分布：
    * 虚拟化变更：U条（影响实例：T个）
    * 网络变更：S条（影响实例：R个）
  - 关键变更时间点：
    * 变更高峰期：YYYY-MM-DD HH:MM:SS ~ YYYY-MM-DD HH:MM:SS
    * 故障前最近变更：YYYY-MM-DD HH:MM:SS（变更类型：XXX）
  - 变更影响评估：
    * 直接影响实例：[instance-1, instance-2, ...]
    * 间接影响实例：[instance-3, instance-4, ...]
  - 变更关联结论：[变更导致批量故障/变更无关]
  ```

### 步骤4：批量监控异常事件识别
**目标**：识别批量实例的异常事件，确定故障的准确时间窗口和异常模式
- **工具**：`listMonitorExceptions`
- **输入参数**：
  - **machineId**：批量实例ID列表（逐个调用或批量传入）
  - **startTime**：问题发生时间前3小时（格式：YYYY-MM-DD HH:MM:SS）
  - **endTime**：问题发生时间后1小时（格式：YYYY-MM-DD HH:MM:SS）
- **执行策略**：对每个实例分别查询异常事件，然后进行批量时间关联分析
- **分析要点**：
  - 收集所有实例的监控异常事件
  - 识别异常事件的时间聚集性
  - 分析异常类型的一致性和差异性
  - 确定最准确的故障时间窗口（T0时间点）
  - 识别异常事件的传播模式
- **标准输出格式**：
  ```
  【批量监控异常分析】
  - 监控查询时间范围：YYYY-MM-DD HH:MM:SS ~ YYYY-MM-DD HH:MM:SS
  - 异常事件总数：X条
  - 异常类型分布：
    * CPU异常：Y条（涉及实例：Z个）
    * 内存异常：W条（涉及实例：V个）
    * 网络异常：U条（涉及实例：T个）
    * 磁盘异常：S条（涉及实例：R个）
    * 可用性异常：Q条（涉及实例：P个）
  - 关键时间点识别：
    * 首个异常时间（T0）：YYYY-MM-DD HH:MM:SS
    * 异常高峰期：YYYY-MM-DD HH:MM:SS ~ YYYY-MM-DD HH:MM:SS
    * 最后异常时间：YYYY-MM-DD HH:MM:SS
  - 异常传播模式：
    * 同时发生：[instance-1, instance-2, ...]（时间差<5分钟）
    * 级联发生：[instance-3→instance-4→instance-5]（时间间隔：X分钟）
  - 故障时间窗口：YYYY-MM-DD HH:MM:SS ~ YYYY-MM-DD HH:MM:SS（持续时间：X分钟）
  ```

### 步骤5：批量冷迁移记录分析
**目标**：如果步骤1发现了冷迁移事件，深入分析批量实例冷迁移的具体原因和触发机制。
- **工具**：`listColdMigrationRecords`
- **输入参数**：
  - **instanceId**：批量实例ID列表（逐个调用）
  - **startTime**：问题发生时间前2小时（格式：YYYY-MM-DD HH:MM:SS）
  - **endTime**：问题发生时间后1小时（格式：YYYY-MM-DD HH:MM:SS）
- **执行策略**：对每个涉及冷迁移的实例分别查询迁移记录，然后进行批量关联分析
- **分析要点**：
  - 收集所有实例的冷迁移触发原因
  - 识别批量冷迁移的共同触发因素
  - 分析冷迁移决策的时间集中性
  - 评估冷迁移过程对批量不可用的影响（需要停机）
  - 识别冷迁移级联效应和关联性
- **标准输出格式**：
  ```
  【批量冷迁移记录分析】
  - 涉及冷迁移实例总数：X个
  - 冷迁移详细记录：
    * 实例ID: i-xxxxx1 | 源NC IP: ************ | 目标NC IP: ************ | 迁移原因: 计划维护 | 开始时间: YYYY-MM-DD HH:MM:SS | 结束时间: YYYY-MM-DD HH:MM:SS
    * 实例ID: i-xxxxx2 | 源NC IP: ************ | 目标NC IP: ************ | 迁移原因: 硬件故障 | 开始时间: YYYY-MM-DD HH:MM:SS | 结束时间: YYYY-MM-DD HH:MM:SS
    * [更多记录...]
  - 冷迁移原因分类统计：
    * 计划维护迁移：Y个实例 [instance-1, instance-2, ...]
    * 硬件故障迁移：Z个实例 [instance-3, instance-4, ...]
    * 资源调度迁移：W个实例 [instance-5, instance-6, ...]
    * 紧急迁移：V个实例 [instance-7, instance-8, ...]
  - 主要冷迁移原因：[原因名称]（占比：XX%）

### 步骤6：批量热迁移记录分析
**目标**：如果步骤1发现了热迁移事件，深入分析批量实例热迁移的具体原因和触发机制。
- **工具**：`listLiveMigrationRecords`
- **输入参数**：
  - **instanceId**：批量实例ID列表（逐个调用）
  - **startTime**：问题发生时间前2小时（格式：YYYY-MM-DD HH:MM:SS）
  - **endTime**：问题发生时间后1小时（格式：YYYY-MM-DD HH:MM:SS）
- **执行策略**：对每个涉及热迁移的实例分别查询迁移记录，然后进行批量关联分析
- **分析要点**：
  - 收集所有实例的热迁移触发原因
  - 识别批量热迁移的共同触发因素
  - 分析热迁移决策的时间集中性
  - 评估热迁移过程对批量不可用的影响（在线迁移，理论上无需停机）
  - 识别热迁移级联效应和关联性
- **标准输出格式**：
  ```
  【批量热迁移记录分析】
  - 涉及热迁移实例总数：X个
  - 热迁移详细记录：
    * 实例ID: i-yyyyy1 | 源NC IP: ************ | 目标NC IP: ************ | 迁移原因: 计划维护 | 开始时间: YYYY-MM-DD HH:MM:SS | 结束时间: YYYY-MM-DD HH:MM:SS
    * 实例ID: i-yyyyy2 | 源NC IP: ************ | 目标NC IP: ************ | 迁移原因: 资源调度 | 开始时间: YYYY-MM-DD HH:MM:SS | 结束时间: YYYY-MM-DD HH:MM:SS
    * [更多记录...]
  - 热迁移原因分类统计：
    * 计划维护迁移：Y个实例 [instance-1, instance-2, ...]
    * 硬件故障迁移：Z个实例 [instance-3, instance-4, ...]
    * 资源调度迁移：W个实例 [instance-5, instance-6, ...]
    * 紧急迁移：V个实例 [instance-7, instance-8, ...]
  - 主要热迁移原因：[原因名称]（占比：XX%）
  - 迁移时间分布：
    * 迁移启动时间：YYYY-MM-DD HH:MM:SS ~ YYYY-MM-DD HH:MM:SS
    * 迁移完成时间：YYYY-MM-DD HH:MM:SS ~ YYYY-MM-DD HH:MM:SS
    * 迁移持续时间：平均X分钟，最长Y分钟
  - 迁移执行状态：
    * 成功迁移：U个实例
    * 失败迁移：T个实例
    * 部分成功：S个实例
  - 迁移影响评估：
    * 直接导致不可用：R个实例
    * 间接影响：Q个实例
  - 批量迁移结论：[统一触发的批量迁移/独立触发的并发迁移]
  ```
