import logging
import json
import json_repair

logger = logging.getLogger(__name__)


def repair_json_output(content: str) -> str:
    """
    Repair and normalize JSON output.

    Args:
        content (str): String content that may contain JSON

    Returns:
        str: Repaired JSON string, or original content if not JSON
    """
    content = content.strip()
    if content.startswith(("{", "[")) or "```json" in content or "```ts" in content:
        try:
            # If content is wrapped in ```json code block, extract the JSON part
            if content.startswith("```json"):
                content = content.removeprefix("```json")

            if content.startswith("```ts"):
                content = content.removeprefix("```ts")

            if content.endswith("```"):
                content = content.removesuffix("```")

            # Try to repair and parse JSON
            repaired_content = json_repair.loads(content)
            return json.dumps(repaired_content, ensure_ascii=False)
        except Exception as e:
            logger.warning(f"JSON repair failed: {e}")
    return content


def extract_json_code_block(text: str) -> str:
    """
    提取文本中由 ```json 包裹的 JSON 内容。
    
    Args:
        text (str): 需要提取 JSON 的原始文本
        
    Returns:
        str: 提取出的 JSON 字符串，如果没有找到则返回空字符串
    """
    lines = text.splitlines()
    json_line = []
    find_start = False

    for line in lines:
        # 检测到 JSON 代码块起始标记
        if not find_start and line.startswith("```json"):
            find_start = True
        # 检测到 JSON 代码块结束标记，停止提取
        elif find_start and line.startswith("```"):
            break
        # 收集 JSON 代码块内容
        elif find_start:
            json_line.append(line)
    return "\n".join(json_line)


def extract_items_from_incomplete_json(json_list_str: str) -> (list, int):
    """
    解析可能包含不完整JSON对象的字符串列表，提取所有有效的JSON对象。

    Args:
        json_list_str (str): 包含潜在JSON对象的字符串

    Returns:
        list: 成功解析的所有JSON对象组成的列表
        int: 本次解析的字符数
    """
    results = []
    # 寻找第一个'{'和最后一个'}'之间的内容作为潜在JSON范围
    start_index = json_list_str.find('{')
    end_index = json_list_str.rfind('}')

    if start_index == -1 or end_index == -1:
        return results, 0  # 没有找到有效的 JSON 对象

    # 提取核心JSON区域并去除换行符以简化处理
    analysed_chars = end_index + 1
    potential_json = json_list_str[start_index:end_index + 1]
    potential_json = potential_json.replace('\n', '')  # 去除换行符

    # 循环尝试解析多个嵌套或连续的JSON对象
    while '{' in potential_json:
        start_index = potential_json.find('{')
        end_index = potential_json.find('}', start_index)
        if end_index == -1:
            break  # 当前部分找不到闭合括号，结束解析
        try:
            # 尝试解析当前定位的JSON对象
            obj = json.loads(potential_json[start_index:end_index + 1])
            results.append(obj)
            # 截断已解析的部分，继续后续处理
            potential_json = potential_json[end_index + 1:]
        except json.JSONDecodeError:
            # 跳过无法解析的内容，继续下一轮查找
            potential_json = potential_json[start_index + 1:]

    return results, analysed_chars


def to_one_line_json(target_dict: dict) -> str:
    """
    将字典转换为单行JSON字符串。

    Args:
        target_dict (dict): 需要转换的字典对象

    Returns:
        str: 转换后的单行JSON字符串
    """
    json_str = json.dumps(target_dict, ensure_ascii=False)
    return json_str.replace("\n", "")
