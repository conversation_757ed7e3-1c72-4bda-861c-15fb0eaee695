"""
LLM实例管理模块

提供LLM实例的创建、缓存和fallback机制管理功能。
支持多种LLM类型，包括reasoning、basic、vision、code等。
"""

from typing import Union
import logging

from langchain_openai import ChatOpenAI
from langchain_core.runnables.fallbacks import RunnableWithFallbacks

from deep_diagnose.common.config import get_config
from deep_diagnose.llms.agent_llm_config import LLMType
from deep_diagnose.common.config.core.base_config import DotDict
from deep_diagnose.llms.chat_qwen import ChatQwen

# 设置日志
logger = logging.getLogger(__name__)

# LLM实例缓存
_llm_cache: dict[LLMType, ChatOpenAI] = {}
# 带fallback机制的LLM实例缓存
_llm_fallback_cache: dict[LLMType, Union[ChatOpenAI, RunnableWithFallbacks]] = {}


def _get_llm_profile_config(llm_type: LLMType, conf: DotDict) -> dict:
    """
    获取指定LLM类型的配置信息
    
    Args:
        llm_type: LLM类型
        conf: 配置对象
        
    Returns:
        LLM配置字典
        
    Raises:
        ValueError: 当LLM类型未知或配置无效时
    """
    llm_profile_mapping = {
        "reasoning": conf.llm.profiles.reasoning,
        "basic": conf.llm.profiles.basic,
        "vision": conf.llm.profiles.vision,
        "code_flash": conf.llm.profiles.code_flash,
        "glm": conf.llm.profiles.glm,
        "code_max": conf.llm.profiles.code_max,
        "kimi_k2": conf.llm.profiles.kimi_k2,
        "deepseek_v31": conf.llm.profiles.deepseek_v31,
        "code": getattr(conf.llm.profiles, 'code', conf.llm.profiles.reasoning),
    }
    
    llm_config = llm_profile_mapping.get(llm_type)
    if not llm_config:
        raise ValueError(f"Unknown LLM type: {llm_type}")
    if not isinstance(llm_config, dict):
        raise ValueError(f"Invalid LLM configuration for type: {llm_type}")

    return llm_config


def _create_llm_from_config(llm_type: LLMType, conf: DotDict) -> ChatOpenAI:
    """
    根据配置创建LLM实例
    
    Args:
        llm_type: LLM类型
        conf: 配置对象
        
    Returns:
        ChatOpenAI实例
    """
    llm_config = _get_llm_profile_config(llm_type, conf)
    
    # 过滤掉fallback_models字段，避免传递给ChatQwen构造函数
    filtered_config = {k: v for k, v in llm_config.items() if k != 'fallback_models'}
    
    return ChatQwen(**filtered_config)


def get_basic_llm_instance(llm_type: LLMType) -> ChatOpenAI:
    """
    获取基础LLM实例（不带fallback机制）
    
    Args:
        llm_type: LLM类型
        
    Returns:
        ChatOpenAI实例，如果缓存中存在则返回缓存实例
    """
    # 检查缓存
    if llm_type in _llm_cache:
        logger.debug(f"返回缓存的LLM实例: {llm_type}")
        return _llm_cache[llm_type]
    
    # 创建新实例
    conf = get_config()
    llm_instance = _create_llm_from_config(llm_type, conf)
    
    # 缓存实例
    _llm_cache[llm_type] = llm_instance
    logger.info(f"创建并缓存新的LLM实例: {llm_type}")
    
    return llm_instance


def _get_fallback_model_names(llm_type: LLMType, conf: DotDict) -> list[str]:
    """
    获取fallback模型名称列表
    
    从配置文件读取fallback_models配置
    
    Args:
        llm_type: 主要LLM类型
        conf: 配置对象
        
    Returns:
        fallback模型名称列表，按优先级排序
    """
    # 从配置文件读取fallback_models
    try:
        llm_config = _get_llm_profile_config(llm_type, conf)
        if 'fallback_models' in llm_config:
            fallback_models = llm_config['fallback_models']
            logger.debug(f"从配置文件获取fallback模型: {llm_type} -> {fallback_models}")
            return fallback_models
        else:
            logger.warning(f"配置文件中未找到fallback_models配置: {llm_type}")
            return []
    except (AttributeError, KeyError, ValueError) as e:
        logger.error(f"从配置文件读取fallback模型失败: {e}")
        return []


def _create_llm_from_model_name(model_name: str, conf: DotDict) -> ChatOpenAI:
    """
    根据模型名称创建LLM实例
    
    Args:
        model_name: 模型名称
        conf: 配置对象
        
    Returns:
        ChatOpenAI实例
    """
    base_config = {
        "base_url": conf.llm.tongyi_provider.base_url,
        "api_key": conf.llm.tongyi_provider.api_key,
        "model": model_name
    }
    
    logger.debug(f"创建fallback LLM实例: {model_name}")
    return ChatQwen(**base_config)


def get_llm_by_type(llm_type: LLMType) -> Union[ChatOpenAI, RunnableWithFallbacks]:
    """
    获取LLM实例（推荐使用，默认启用fallback机制）
    
    Args:
        llm_type: LLM类型
        
    Returns:
        LLM实例，可能带有fallback机制
    """
    return get_llm_with_fallback(llm_type, fallback_enable=True)


def get_llm_with_fallback(
    llm_type: LLMType,
    fallback_enable: bool = True
) -> Union[ChatOpenAI, RunnableWithFallbacks]:
    """
    获取带有fallback机制的LLM实例
    
    使用LangChain原生的with_fallbacks能力，提供更好的容错性
    
    Args:
        llm_type: LLM类型
        fallback_enable: 是否启用fallback机制
        
    Returns:
        LLM实例，如果启用fallback且有fallback模型，返回RunnableWithFallbacks
    """
    # 如果不启用fallback，直接返回基础实例
    if not fallback_enable:
        logger.debug(f"fallback机制已禁用，返回基础LLM实例: {llm_type}")
        return get_basic_llm_instance(llm_type)

    # 检查缓存
    if llm_type in _llm_fallback_cache:
        logger.debug(f"返回缓存的fallback LLM实例: {llm_type}")
        return _llm_fallback_cache[llm_type]

    conf = get_config()
    
    # 获取主要LLM实例
    primary_llm = get_basic_llm_instance(llm_type)

    # 获取fallback模型名称列表
    fallback_model_names = _get_fallback_model_names(llm_type, conf)

    # 如果没有fallback模型，直接返回主要LLM
    if not fallback_model_names:
        logger.info(f"没有配置fallback模型，返回基础LLM实例: {llm_type}")
        _llm_fallback_cache[llm_type] = primary_llm
        return primary_llm

    # 创建fallback模型列表
    fallback_models = []
    for model_name in fallback_model_names:
        try:
            fallback_llm = _create_llm_from_model_name(model_name, conf)
            fallback_models.append(fallback_llm)
            logger.debug(f"成功创建fallback模型: {model_name}")
        except Exception as e:
            logger.warning(f"创建fallback模型失败 {model_name}: {e}")

    # 如果有可用的fallback模型，创建带fallback的LLM
    if fallback_models:
        llm_with_fallback = primary_llm.with_fallbacks(fallback_models)
        _llm_fallback_cache[llm_type] = llm_with_fallback
        logger.info(f"创建带fallback机制的LLM实例: {llm_type}, fallback数量: {len(fallback_models)}")
        return llm_with_fallback
    else:
        logger.warning(f"所有fallback模型创建失败，返回基础LLM实例: {llm_type}")
        _llm_fallback_cache[llm_type] = primary_llm
        return primary_llm


def clear_llm_cache():
    """
    清空LLM缓存
    
    在配置更新或需要重新初始化LLM实例时使用
    """
    global _llm_cache, _llm_fallback_cache
    _llm_cache.clear()
    _llm_fallback_cache.clear()
    logger.info("已清空LLM缓存")


