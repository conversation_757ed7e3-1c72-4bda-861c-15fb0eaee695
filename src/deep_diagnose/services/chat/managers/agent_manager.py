"""
Chat Agent Manager

处理Agent创建和配置
"""

import logging
from typing import Dict, Any, Optional

from langfuse.callback import CallbackHand<PERSON>
from langfuse import <PERSON><PERSON>

from deep_diagnose.common.config import get_config
from deep_diagnose.core.agent.agent_factory import AgentFactory
from deep_diagnose.services.chat.managers.session_manager import SessionInfo
from deep_diagnose.services.chat.managers.session_manager import ChatContext

logger = logging.getLogger(__name__)


class AgentManager:
    """Agent管理器 - 处理Agent创建和配置"""

    @staticmethod
    def create_langfuse_handler(
        session_id: Optional[str] = None,
        user_id: Optional[str] = None,
        trace_name: Optional[str] = None,
        trace_id: Optional[str] = None
    ) -> CallbackHandler:
        """创建Langfuse处理器"""
        config = get_config()
        
        # 如果提供了自定义 trace_id，创建一个带有该 ID 的 stateful trace client
        if trace_id:
            try:
                langfuse_client = Langfuse(
                    public_key=config.observability.langfuse.public_key,
                    secret_key=config.observability.langfuse.secret_key,
                    host=config.observability.langfuse.endpoint
                )
                # 创建带有自定义 ID 的 trace
                stateful_trace = langfuse_client.trace(
                    id=trace_id,
                    name=trace_name,
                    session_id=session_id,
                    user_id=user_id
                )
                return CallbackHandler(stateful_client=stateful_trace)
            except Exception as e:
                logger.warning(f"Failed to create Langfuse handler with custom trace_id {trace_id}: {e}")
                # 回退到默认方式
        
        # 默认方式（不使用自定义 trace_id）
        return CallbackHandler(
            public_key=config.observability.langfuse.public_key,
            secret_key=config.observability.langfuse.secret_key,
            host=config.observability.langfuse.endpoint,
            session_id=session_id,
            user_id=user_id,
            trace_name=trace_name
        )

    @staticmethod
    def create_agent(
        agent_type: str, 
        message_id: int,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None,
        context:ChatContext = None
    ):
        """创建Agent实例"""
        # 打印 ChatContext 内容用于调试
        if context:
            logger.info(f"ChatContext details: question='{context.question[:100]}...', "
                       f"user_id='{context.user_id}', agent='{context.agent}', "
                       f"session_id='{context.session_id}', request_id='{context.request_id}', "
                       f"messages_count={len(context.messages) if context.messages else 0}, "
                       f"kwargs_keys={list(context.kwargs.keys()) if context.kwargs else []}")
        else:
            logger.info("ChatContext is None")
        
        # 直接访问 context.request_id，如果不存在或为空则使用 message_id
        if context and context.request_id:
            request_id = context.request_id
        else:
            request_id = str(message_id) if message_id else None
        
        # 只有当 request_id 是有意义的值时才作为 trace_id 传递
        langfuse_handler = AgentManager.create_langfuse_handler(
            session_id=session_id,
            user_id=user_id,
            trace_name=agent_type,  # trace_name对应agent名字
            trace_id=request_id       # 将有效的request_id作为trace_id注入
        )
        config = {
            "langfuse_handler": langfuse_handler,
            "request_id": request_id
        }
        agent = AgentFactory.create_agent(agent_type, config)
        logger.info(f"Created agent: {agent_type} with request_id: {config['request_id']}, trace_id: {request_id}")
        return agent

    @staticmethod
    def prepare_agent_kwargs(context, session_info: SessionInfo) -> Dict[str, Any]:
        """准备Agent调用参数"""
        agent_kwargs = context.kwargs.copy()
        # 直接访问 context.request_id，如果不存在或为空则使用 session_info.message_id
        if context and context.request_id:
            agent_kwargs["request_id"] = context.request_id
        else:
            agent_kwargs["request_id"] = str(session_info.message_id)
        agent_kwargs["user_id"] = context.user_id

        if context.agent == "InteractiveAgent":
            agent_kwargs["resource_ids"] = session_info.resource_ids
            agent_kwargs["resource_type"] = session_info.resource_type
            agent_kwargs["ext"] = session_info.ext

        elif context.agent == "InspectAgent":
            agent_kwargs["resource_ids"] = session_info.resource_ids
            agent_kwargs["resource_type"] = session_info.resource_type
            agent_kwargs["ext"] = session_info.ext
            agent_kwargs["session_id"] = session_info.session_id
            context.question = f"资源ID: {(session_info.resource_ids)} 资源类型: {session_info.resource_type}\n\n" + context.question

        return agent_kwargs