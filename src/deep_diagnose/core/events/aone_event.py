import json
from dataclasses import dataclass

from deep_diagnose.common.utils.string_utils import substring_after
from deep_diagnose.core.events.base_event import BaseAgentOutputEvent
from deep_diagnose.core.interactive.state import AoneState


@dataclass
@BaseAgentOutputEvent.register_event_type("aone")
class AoneEvent(BaseAgentOutputEvent):
    """事件 - 累积式状态容器"""
    summary: str = ""
    solution: str = ""
    duty: str = ""
    finished: bool = False  # 输出结束标识

    def parse(self, raw_event: str) -> bool:
        return False

    def parse_graph_event(self, state: AoneState) -> bool:
        """
        从AoneState对象中解析并更新事件属性
        
        Args:
            state (AoneState): 包含解决方案、摘要、负责人和完成状态的Aone状态对象
            
        Returns:
            bool: 总是返回True，表示解析成功
        """
        self.solution = substring_after(state.solution, "## 回复\n\n")
        self.summary = state.summary
        self.duty = state.duty
        self.finished = state.finished
        return True

    def to_sse_format(self) -> str:
        """
        将事件序列化为 SSE 格式
        """
        payload = {
            "solution": self.solution,
            "summary": self.summary,
            "duty": self.duty
        }

        return json.dumps(payload, ensure_ascii=False)
