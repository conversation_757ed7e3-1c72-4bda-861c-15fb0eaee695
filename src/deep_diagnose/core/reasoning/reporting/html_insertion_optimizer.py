#!/usr/bin/env python3
"""
HTML插入优化器

## 角色与目标
专门负责优化HTML报告中性能数据section的插入逻辑，确保插入位置准确、HTML结构完整。

## 核心功能
1. **智能位置检测**：准确识别总结建议section的结束位置
2. **结构完整性**：确保插入后HTML结构完整
3. **脚本去重**：避免重复的CSS/JS引用
4. **错误恢复**：提供多种插入策略的回退机制
"""

import re
import logging
from typing import Optional, Tuple, List
from bs4 import BeautifulSoup, Comment

logger = logging.getLogger(__name__)


class HTMLInsertionOptimizer:
    """HTML插入优化器"""
    
    def __init__(self):
        self.insertion_strategies = [
            self._strategy_slide_format_insert,  # 优先：幻灯片格式插入，支持分页导航
            self._strategy_append_as_appendix,  # 备用：将性能数据作为附录添加
            self._strategy_before_body_end  # 最后：直接在body末尾插入
        ]
    
    def optimize_performance_insertion(self, html_content: str, performance_section: str, request_id: str) -> str:
        """
        优化性能数据section的插入
        
        Args:
            html_content: 原始HTML内容
            performance_section: 性能数据section HTML
            request_id: 请求ID
            
        Returns:
            str: 优化后的HTML内容
        """
        try:
            # 1. 预处理性能section，移除重复的脚本引用
            cleaned_performance_section = self._clean_performance_section(performance_section, html_content)
            
            # 2. 尝试各种插入策略
            for i, strategy in enumerate(self.insertion_strategies):
                try:
                    result = strategy(html_content, cleaned_performance_section)
                    if result:
                        logger.info(f"成功使用策略 {i+1} 插入性能数据，请求ID: {request_id}")
                        
                        # 3. 验证插入结果
                        if self._validate_html_structure(result):
                            return result
                        else:
                            logger.warning(f"策略 {i+1} 插入后HTML结构验证失败")
                            continue
                            
                except Exception as e:
                    logger.warning(f"策略 {i+1} 执行失败: {e}")
                    continue
            
            # 4. 所有策略都失败，返回原始内容
            logger.error(f"所有插入策略都失败，返回原始HTML，请求ID: {request_id}")
            return html_content
            
        except Exception as e:
            logger.error(f"HTML插入优化失败: {e}")
            return html_content
    
    def _clean_performance_section(self, performance_section: str, original_html: str) -> str:
        """
        清理性能section，移除HTML文档结构和重复脚本引用
        
        Args:
            performance_section: 原始性能section
            original_html: 原始HTML内容
            
        Returns:
            str: 清理后的性能section
        """
        try:
            cleaned_section = performance_section
            
            # 1. 移除完整的HTML文档结构
            cleaned_section = re.sub(r'<!DOCTYPE[^>]*>', '', cleaned_section, flags=re.IGNORECASE)
            cleaned_section = re.sub(r'<html[^>]*>', '', cleaned_section, flags=re.IGNORECASE)
            cleaned_section = re.sub(r'</html>', '', cleaned_section, flags=re.IGNORECASE)
            
            # 2. 移除<head>部分（包括所有子元素）
            cleaned_section = re.sub(r'<head[^>]*>.*?</head>', '', cleaned_section, flags=re.DOTALL | re.IGNORECASE)
            
            # 3. 移除<body>标签，保留内容
            cleaned_section = re.sub(r'<body[^>]*>', '', cleaned_section, flags=re.IGNORECASE)
            cleaned_section = re.sub(r'</body>', '', cleaned_section, flags=re.IGNORECASE)
            
            # 4. 检查原始HTML中是否已包含相关脚本
            has_tailwind = 'cdn.tailwindcss.com' in original_html
            has_chartjs = 'cdn.jsdelivr.net/npm/chart.js' in original_html
            
            # 5. 移除重复的脚本引用
            if has_tailwind:
                cleaned_section = re.sub(
                    r'<script[^>]*cdn\.tailwindcss\.com[^>]*></script>\s*',
                    '',
                    cleaned_section,
                    flags=re.IGNORECASE
                )
            
            if has_chartjs:
                cleaned_section = re.sub(
                    r'<script[^>]*cdn\.jsdelivr\.net/npm/chart\.js[^>]*></script>\s*',
                    '',
                    cleaned_section,
                    flags=re.IGNORECASE
                )
            
            # 6. 确保Chart.js在需要时被添加
            if not has_chartjs and 'Chart(' in cleaned_section:
                chart_script = '<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>\n        '
                # 在第一个<script>标签前插入Chart.js
                script_match = re.search(r'<script[^>]*>', cleaned_section)
                if script_match:
                    insert_pos = script_match.start()
                    cleaned_section = (
                        cleaned_section[:insert_pos] + 
                        chart_script + 
                        cleaned_section[insert_pos:]
                    )
                else:
                    # 如果没有其他script标签，在section开始处添加
                    cleaned_section = chart_script + cleaned_section
            
            # 7. 清理多余空白
            cleaned_section = re.sub(r'\n\s*\n+', '\n', cleaned_section)  # 多个空行合并
            cleaned_section = cleaned_section.strip()
            
            # 8. 验证清理结果
            if not cleaned_section:
                logger.warning("性能section清理后为空，返回包装的默认内容")
                return '<div class="text-slate-400">性能数据暂无内容</div>'
            
            return cleaned_section
            
        except Exception as e:
            logger.error(f"清理性能section失败: {e}")
            return performance_section
    
    def _strategy_append_as_appendix(self, html_content: str, performance_section: str) -> Optional[str]:
        """
        策略1: 将性能数据作为附录添加到报告末尾
        """
        # 查找</body>标签位置
        body_end = html_content.rfind('</body>')
        if body_end != -1:
            # 在</body>标签前插入性能数据section
            result = (
                html_content[:body_end] + 
                '\n    ' + 
                performance_section + 
                '\n    ' + 
                html_content[body_end:]
            )
            return result
        
        return None
    
    def _strategy_before_body_end(self, html_content: str, performance_section: str) -> Optional[str]:
        """
        策略2: 在</body>标签之前插入
        """
        body_end = html_content.rfind('</body>')
        if body_end != -1:
            result = (
                html_content[:body_end] + 
                '\n    ' + 
                performance_section + 
                '\n    ' + 
                html_content[body_end:]
            )
            return result
        
        return None
    
    def _strategy_slide_format_insert(self, html_content: str, performance_section: str) -> Optional[str]:
        """
        策略3: 为幻灯片格式插入性能数据作为新的slide，并更新分页系统
        """
        try:
            # 1. 查找当前最大的slide索引
            slide_matches = re.findall(r'data-slide="(\d+)"', html_content)
            if slide_matches:
                max_slide_index = max(int(match) for match in slide_matches)
                next_slide_index = max_slide_index + 1
            else:
                next_slide_index = 0
            
            # 2. 查找最后一个</section>标签
            last_section_end = html_content.rfind('</section>')
            if last_section_end != -1:
                # 在最后一个section后插入新的性能数据section
                insert_pos = last_section_end + len('</section>')
                
                # 包装性能数据为标准slide格式，添加slide类和data-slide属性
                slide_performance = f"""

    <!-- Slide {next_slide_index + 1}: 性能数据附录 -->
    <section class="slide min-h-screen flex flex-col justify-center px-6 md:px-20 py-12 relative compact-section" data-slide="{next_slide_index}">
        <header class="pb-3 border-b border-slate-700 mb-4 compact-spacing">
            <h1 class="text-2xl font-bold text-sky-400">附录A. 性能数据分析</h1>
        </header>
        <div class="space-y-4 flex-grow flex flex-col justify-center compact-spacing section-content">
            {performance_section}
        </div>
        <footer class="absolute bottom-8 right-8 text-sm text-slate-600">Slide {next_slide_index + 1} of {next_slide_index + 1}</footer>
    </section>"""
                
                # 3. 插入性能数据slide
                html_with_slide = (
                    html_content[:insert_pos] + 
                    slide_performance + 
                    html_content[insert_pos:]
                )
                
                # 4. 更新分页控制器
                result = self._update_slide_navigation(html_with_slide, next_slide_index + 1)
                return result
            
            # 如果没找到section标签，回退到body末尾插入
            body_end = html_content.rfind('</body>')
            if body_end != -1:
                slide_performance = f"""
    <!-- 性能数据附录 -->
    <section class="min-h-screen flex flex-col justify-center px-6 md:px-20 py-12 relative compact-section">
        <header class="pb-3 border-b border-slate-700 mb-4 compact-spacing">
            <h1 class="text-2xl font-bold text-sky-400">附录A. 性能数据分析</h1>
        </header>
        <div class="space-y-4 flex-grow flex flex-col justify-center compact-spacing">
            {performance_section}
        </div>
        <footer class="absolute bottom-8 right-8 text-sm text-slate-600">性能数据附录</footer>
    </section>"""
                
                result = (
                    html_content[:body_end] + 
                    slide_performance + 
                    '\n' + 
                    html_content[body_end:]
                )
                return result
                
        except Exception as e:
            logger.warning(f"策略3执行异常: {e}")
            return None
        
        return None
    
    def _update_slide_navigation(self, html_content: str, total_slides: int) -> str:
        """
        更新幻灯片导航系统，包括分页控制器和导航点
        
        Args:
            html_content: HTML内容
            total_slides: 总幻灯片数量
            
        Returns:
            str: 更新后的HTML内容
        """
        try:
            result = html_content
            
            # 1. 更新总页数显示 (更新JavaScript中的totalSlides变量)
            result = re.sub(
                r'<span id="totalSlides">\d+</span>',
                f'<span id="totalSlides">{total_slides}</span>',
                result
            )
            
            # 2. 使用更精确的方法删除现有导航点
            # 精确匹配导航点注释和容器，避免误删其他内容
            result = re.sub(r'<!-- 导航点 -->\s*\n\s*<div class="slide-dots">(?:[^<]|<(?!/div>))*</div>', '', result, flags=re.DOTALL)
            # 如果上述模式没有匹配到，尝试只删除slide-dots容器
            result = re.sub(r'<div class="slide-dots"[^>]*>(?:[^<]|<(?!/?div))*(?:<div[^>]*>[^<]*</div>)*[^<]*</div>', '', result, flags=re.DOTALL)
            # 清理孤立的导航点（如果有的话）
            result = re.sub(r'<div class="slide-dot[^"]*"[^>]*data-slide="[^"]*"[^>]*></div>', '', result, flags=re.DOTALL)
            
            # 在合适位置插入新的导航点
            # 直接在第一个section前插入，这样更可靠
            first_section = result.find('<section')
            if first_section != -1:
                new_dots_html = '\n    <!-- 导航点 -->\n    <div class="slide-dots">'
                for i in range(total_slides):
                    active_class = ' active' if i == 0 else ''
                    new_dots_html += f'\n        <div class="slide-dot{active_class}" data-slide="{i}"></div>'
                new_dots_html += '\n    </div>\n\n'
                
                result = result[:first_section] + new_dots_html + result[first_section:]
                logger.info(f"成功插入导航点容器，总共{total_slides}个导航点")
            else:
                logger.warning("未找到合适的插入位置，跳过导航点更新")
            
            # 3. 更新JavaScript中的slides查询和总数
            # 查找并更新JavaScript部分  
            js_pattern = r'let totalSlides = slides\.length;'
            js_replacement = f'let totalSlides = {total_slides}; // 更新总页数，包含性能数据页面'
            result = re.sub(js_pattern, js_replacement, result)
            
            # 4. 确保所有footer中的页数都正确更新
            footer_pattern = r'(Slide \d+ of )\d+'
            footer_replacement = f'\\g<1>{total_slides}'
            result = re.sub(footer_pattern, footer_replacement, result)
            
            logger.info(f"成功更新幻灯片导航系统，总页数: {total_slides}")
            return result
            
        except Exception as e:
            logger.error(f"更新幻灯片导航失败: {e}")
            return html_content
    
    def _strategy_append_to_content(self, html_content: str, performance_section: str) -> Optional[str]:
        """
        策略4: 直接追加到HTML末尾（最后的回退策略）
        """
        # 在</html>之前插入
        html_end = html_content.rfind('</html>')
        if html_end != -1:
            result = (
                html_content[:html_end] + 
                '\n' + 
                performance_section + 
                '\n' + 
                html_content[html_end:]
            )
            return result
        else:
            # 直接追加
            return html_content + '\n' + performance_section
    
    def _validate_html_structure(self, html_content: str) -> bool:
        """
        验证HTML结构的完整性
        
        Args:
            html_content: HTML内容
            
        Returns:
            bool: 结构是否完整
        """
        try:
            # 更精确的标签配对检查 - 只检查顶层结构标签
            import re
            
            # 检查顶层 HTML 结构标签（避免内容中的HTML代码干扰）
            html_open = len(re.findall(r'<html[^>]*>', html_content, re.IGNORECASE))
            html_close = html_content.count('</html>')
            
            head_open = len(re.findall(r'<head[^>]*>', html_content, re.IGNORECASE))
            head_close = html_content.count('</head>')
            
            body_open = len(re.findall(r'<body[^>]*>', html_content, re.IGNORECASE))
            body_close = html_content.count('</body>')
            
            # 只检查最基本的结构标签配对
            structure_checks = [
                ('html', html_open, html_close),
                ('head', head_open, head_close),
                ('body', body_open, body_close),
            ]
            
            for tag_name, open_count, close_count in structure_checks:
                if open_count != close_count:
                    logger.warning(f"HTML {tag_name} 标签配对不匹配: {open_count} vs {close_count}")
                    # 对于非关键标签不匹配，只警告不失败
                    if tag_name == 'html' and abs(open_count - close_count) > 2:
                        return False  # 只有严重不匹配时才失败
            
            # 检查是否包含基本结构
            required_elements = ['<!DOCTYPE', '<html', '<head', '<body']
            for element in required_elements:
                if element not in html_content:
                    logger.warning(f"缺少必需的HTML元素: {element}")
                    return False
            
            # 检查性能数据是否成功插入 - 更灵活的检查
            performance_indicators = [
                "附录A. 性能数据分析",
                "性能数据",
                "performance",
                "metrics",
                "CPU",
                "内存",
                "磁盘"
            ]
            
            has_performance_data = any(indicator in html_content for indicator in performance_indicators)
            if not has_performance_data:
                logger.warning("性能数据section未成功插入到HTML中")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"HTML结构验证失败: {e}")
            return False
    
    def analyze_html_structure(self, html_content: str) -> dict:
        """
        分析HTML结构，提供调试信息
        
        Args:
            html_content: HTML内容
            
        Returns:
            dict: 结构分析结果
        """
        try:
            analysis = {
                'total_length': len(html_content),
                'sections_found': [],
                'script_tags': [],
                'structure_issues': []
            }
            
            # 查找所有section
            section_patterns = [
                r'<h2[^>]*>([^<]+)</h2>',
                r'<h1[^>]*>([^<]+)</h1>',
            ]
            
            for pattern in section_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                analysis['sections_found'].extend(matches)
            
            # 查找所有script标签
            script_matches = re.findall(r'<script[^>]*src="([^"]+)"[^>]*>', html_content, re.IGNORECASE)
            analysis['script_tags'] = script_matches
            
            # 检查结构问题
            if html_content.count('<html') != html_content.count('</html>'):
                analysis['structure_issues'].append('HTML标签不匹配')
            
            if html_content.count('<body') != html_content.count('</body>'):
                analysis['structure_issues'].append('Body标签不匹配')
            
            # 检查重复脚本
            script_counts = {}
            for script in script_matches:
                script_counts[script] = script_counts.get(script, 0) + 1
            
            for script, count in script_counts.items():
                if count > 1:
                    analysis['structure_issues'].append(f'重复脚本: {script} ({count}次)')
            
            return analysis
            
        except Exception as e:
            logger.error(f"HTML结构分析失败: {e}")
            return {'error': str(e)}


# 创建全局实例
html_insertion_optimizer = HTMLInsertionOptimizer()