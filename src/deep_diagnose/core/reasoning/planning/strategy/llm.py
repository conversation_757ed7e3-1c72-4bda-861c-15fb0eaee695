import logging
import json
import re
from typing import List, Dict, Any

from deep_diagnose.core.reasoning.planning.models import SOP, SelectedSOP
from deep_diagnose.llms.llm import get_llm_by_type
from deep_diagnose.llms.agent_llm_config import AGENT_LLM_MAP, SILENT_LLM_CALL_TAG
from .base import SOPSelectionStrategy

logger = logging.getLogger(__name__)


class LLMSelectionStrategy(SOPSelectionStrategy):
    """SOP selection strategy that uses an LLM to choose the best SOP."""

    def __init__(self, llm_client: Any = None):
        self.llm = llm_client if llm_client else get_llm_by_type(AGENT_LLM_MAP.get("planner", "reasoning"))
        if not self.llm:
            logger.error("LLM client not initialized for LLMSelectionStrategy.")
            raise ValueError("LLM client is required for LLMSelectionStrategy.")

    async def select_sop(self, query: str, sops: List[SOP]) -> SelectedSOP:
        if not sops:
            return SelectedSOP(sop=None, reason="No SOPs available for selection.", success=False)

        try:
            prompt = self._build_llm_prompt(query, sops)
            messages = [{"role": "user", "content": prompt}]
            response = await self.llm.ainvoke(messages, config={"stream": False, "tags": [SILENT_LLM_CALL_TAG]})

            result = self._parse_llm_response(response.content)
            selected_index = result.get("index", 0)
            reason = result.get("reason", "LLM selection")

            if 0 <= selected_index < len(sops):
                selected_sop = sops[selected_index]
                return SelectedSOP(sop=selected_sop, reason=f"LLM选择: {reason}", success=True)
            else:
                # Fallback to the first SOP if index is invalid
                fallback_sop = sops[0]
                return SelectedSOP(sop=fallback_sop, reason=f"LLM返回无效索引，使用默认SOP: {reason}", success=True)

        except Exception as e:
            logger.error(f"LLM selection failed: {e}", exc_info=True)
            # Fallback to the first SOP on error
            fallback_sop = sops[0]
            return SelectedSOP(sop=fallback_sop, reason=f"LLM选择失败，使用默认SOP: {str(e)}", success=False, error_message=str(e))

    def _build_llm_prompt(self, query: str, sops: List[SOP]) -> str:
        # 构建可选SOP方案的XML格式内容
        sop_options = []
        for i, sop in enumerate(sops):
            scenario_text = sop.scenario.replace('\n', ' ').strip() if isinstance(sop.scenario, str) else '无场景描述'
            if len(scenario_text) > 200:
                scenario_text = scenario_text[:200] + "..."

            example_text = '、'.join(sop.example_user_queries[:2]) if sop.example_user_queries else '无示例'

            sop_option = f"""<sop index="{i}">
  <name>{sop.name}</name>
  <scenario>{scenario_text}</scenario>
  <examples>{example_text}</examples>
</sop>"""
            sop_options.append(sop_option)

        prompt = f"""# SOP 智能选择专家

## 角色
您是一位专业的 SOP（标准作业程序）选择专家，专门负责为用户的技术问题匹配最合适的诊断和解决方案。

## 职责
- 深度分析用户问题的技术特征和业务场景
- 精准匹配最适合的 SOP 诊断方案
- 提供清晰、专业的选择理由和建议
- 确保选择的 SOP 能够有效解决用户的实际问题

## 指令

### 1. 问题分析
仔细分析用户问题中的：
- 技术关键词和术语
- 问题的严重程度和紧急性
- 涉及的系统组件或服务
- 可能的故障类型和范围

### 2. SOP 匹配
对比每个可选 SOP 的：
- 适用场景和条件
- 解决问题的针对性
- 执行的复杂度和风险
- 预期的解决效果

### 3. 智能选择
- 优先选择最精确匹配的专用 SOP
- 如果没有完全匹配的专用 SOP，选择 `general_troubleshooting` 作为通用方案
- 确保选择的 SOP 具有可操作性和有效性

## 输入信息

### 用户问题
```
{query}
```

### 可选SOP方案
<sop_options>
{chr(10).join(sop_options)}
</sop_options>

## 输出要求

### 格式规范
请严格按照以下 JSON 格式返回结果：

```json
{{"index": 选择的SOP索引号, "reason": "详细的选择理由"}}
```

### 内容要求
- **index**: 必须是有效的 SOP 索引号（0 到 {len(sops)-1}）
- **reason**: 详细说明选择该 SOP 的理由，包括：
  - 问题特征分析
  - SOP 匹配度评估
  - 选择的优势和预期效果

### 质量标准
- 选择理由要专业、准确、有说服力
- 确保所选 SOP 能够切实解决用户问题
- 如有疑虑，优先选择通用的故障排除方案"""
        
        return prompt

    def _parse_llm_response(self, content: str) -> Dict:
        try:
            content = content.strip()
            json_patterns = [
                r'{{[^{{}}]*"index"[^{{}}]*"reason"[^{{}}]*}}',
                r'{{.*"index".*"reason".*?}}',
                r'{{.*?}}',
            ]

            for pattern in json_patterns:
                json_match = re.search(pattern, content, re.DOTALL)
                if json_match:
                    try:
                        result = json.loads(json_match.group(0))
                        if "index" in result:
                            if "reason" not in result:
                                result["reason"] = "LLM选择"
                            return result
                    except json.JSONDecodeError:
                        continue

            index_match = re.search(r'(?:index|选择|索引).*?(\d+)', content, re.IGNORECASE)
            if index_match:
                index = int(index_match.group(1))
                return {"index": index, "reason": f"从响应中提取索引: {index}"}

        except Exception as e:
            logger.warning(f"LLM response JSON parsing failed: {e}, content: {content[:100]}")

        return {"index": 0, "reason": "Parsing failed, using default option"}
