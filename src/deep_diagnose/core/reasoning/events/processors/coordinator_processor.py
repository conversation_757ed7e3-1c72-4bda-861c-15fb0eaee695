"""
计划器消息处理器

处理计划器Agent的消息，重命名自 PlannerEventProcessor
"""

import logging
from typing import Dict, Any, List
import time
from deep_diagnose.core.events.processors.base_processor import BaseMessageProcessor
from deep_diagnose.core.events.models import AgentMessage
from deep_diagnose.llms.agent_llm_config import REWRITE_LLM_CALL_TAG

logger = logging.getLogger(__name__)


class CoordinatorMessageProcessor(BaseMessageProcessor):
    """计划器消息处理器"""

    def __init__(self, request_id: str = None):
        """
        初始化计划器消息处理器

        Args:
            request_id: 请求ID，用于标识处理上下文
        """
        super().__init__(request_id=request_id)

    def get_agent_name(self) -> str:
        return "coordinator"

    def process_messages(self, messages: List[AgentMessage]) -> Dict[str, Any]:
        try:
            # Reporter 主要负责生成最终报告内容和URLs
            result_content = ""
            rewrite_content = ""
            not_rewrite_content = ""
            has_tool_calls = False
            has_finished_message = False
            
            # A. 去抖：仅当合并间隔超过阈值时才进行合并
            # 只处理已完成的消息，并过滤掉 silent LLM 调用
            for message in messages:
                # 检查消息是否已完成 - 修复：使用正确的字段名 is_finished
                if message.is_finished:
                    has_finished_message = True
                    
                # 1.1 区分 rewrite content 和 non-rewrite content
                if message.content.strip():
                    # 检查 message tags 是否包含 REWRITE_LLM_CALL_TAG
                    if hasattr(message, 'tags') and message.tags and REWRITE_LLM_CALL_TAG in message.tags:
                        # rewrite content
                        rewrite_content += message.content
                    else:
                        # 非 rewrite content
                        not_rewrite_content += message.content
                    
                    result_content += message.content
                    
                # 检查是否有工具调用
                if message.tool_executions:
                    has_tool_calls = True
                    for tool_execution in message.tool_executions:
                        if tool_execution.call_name=='handoff_to_planner' and not result_content:
                            result_content = "正在协调各智能体，制定诊断计划"

            result = {}
            if result_content.strip():
                result["understanding"] = result_content.strip()

            # 2. 修改判断 message 结束标志：使用 not_rewrite_message_content.strip()
            if has_finished_message and not has_tool_calls and not_rewrite_content.strip():
                result["finished"] = True
                logger.info(f"Coordinator message finished without tool calls, setting finished=True")

            return result
        except Exception as e:
            logger.error(f"Error processing reporter messages: {e}", exc_info=True)
            return {}