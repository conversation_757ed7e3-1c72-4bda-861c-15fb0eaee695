"""
重新规划器消息处理器

处理重新规划器Agent的消息
"""

import logging
from typing import Dict, Any, List

from .planning_processor_base import PlanningProcessorBase
from deep_diagnose.core.events.models import AgentMessage

logger = logging.getLogger(__name__)


class ReplannerMessageProcessor(PlanningProcessorBase):
    """重新规划器消息处理器"""

    def __init__(self, request_id: str = None):
        """
        初始化重新规划器消息处理器
        
        Args:
            request_id: 请求ID，用于标识处理上下文
        """
        super().__init__(request_id=request_id)

    def get_agent_name(self) -> str:
        return "replanner"

    def process_messages(self, messages: List[AgentMessage]) -> dict[str, list[Any]] | None:
        """处理重新规划器消息"""
        result = super().process_messages(messages)
        
        # 为重新规划器添加特殊的处理逻辑（如果需要）
        if result:
            # 记录重新规划的步骤数量
            if "plan_steps" in result:
                logger.info(f"Replanner updated {len(result['plan_steps'])} steps")
                
            # 只返回 plan_steps
            return {"plan_steps": result.get("plan_steps", [])}
        
        # 如果没有结果，返回空的 plan_steps
        return None