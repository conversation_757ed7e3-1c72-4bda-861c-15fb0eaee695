#!/usr/bin/env python3
"""
存储服务 - 从html_merger.py提取的存储相关功能

负责文件上传、缓存存储等操作
"""

import tempfile
import os
import asyncio
import json
import logging
from datetime import datetime
from typing import Optional

from deep_diagnose.storage.oss_client import OssClient
from deep_diagnose.storage.redis_client import RedisClient

logger = logging.getLogger(__name__)


class StorageError(Exception):
    """存储异常"""
    pass


class StorageService:
    """存储服务 - 处理文件上传和缓存操作"""
    
    def __init__(self, oss_timeout: float = 30.0, redis_timeout: float = 10.0):
        self.oss_timeout = oss_timeout
        self.redis_timeout = redis_timeout
        self.oss_client = None
        self.redis_client = None
    
    def _get_oss_client(self) -> OssClient:
        """获取OSS客户端实例"""
        if self.oss_client is None:
            self.oss_client = OssClient()
        return self.oss_client
    
    def _get_redis_client(self) -> RedisClient:
        """获取Redis客户端实例"""
        if self.redis_client is None:
            self.redis_client = RedisClient()
        return self.redis_client
    
    async def upload_html_to_oss(self, html_content: str, request_id: str) -> bool:
        """
        上传HTML内容到OSS
        
        Args:
            html_content: HTML内容
            request_id: 请求ID
            
        Returns:
            bool: 上传是否成功
            
        Raises:
            StorageError: 上传失败时抛出
        """
        try:
            return await asyncio.wait_for(
                asyncio.get_event_loop().run_in_executor(
                    None, self._upload_to_oss_sync, html_content, request_id
                ),
                timeout=self.oss_timeout
            )
        except asyncio.TimeoutError:
            raise StorageError(f"OSS上传超时，请求ID: {request_id}")
        except Exception as e:
            raise StorageError(f"OSS上传失败，请求ID: {request_id}: {e}")
    
    def _upload_to_oss_sync(self, html_content: str, request_id: str) -> bool:
        """
        同步上传到OSS
        
        Args:
            html_content: HTML内容
            request_id: 请求ID
            
        Returns:
            bool: 上传是否成功
        """
        try:
            oss_client = self._get_oss_client()
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(
                mode="w", suffix=".html", delete=False, encoding="utf-8"
            ) as temp_file:
                temp_file.write(html_content)
                html_path = temp_file.name
            
            try:
                # 使用简化的命名规则：diagnosis-reports/{request_id}.html
                oss_key = f"diagnosis-reports/{request_id}.html"
                oss_client.upload_file(html_path, oss_key)
                logger.info(f"成功上传HTML到OSS: {oss_key}")
                return True
                
            finally:
                # 清理临时文件
                if os.path.exists(html_path):
                    os.unlink(html_path)
                    
        except Exception as e:
            logger.error(f"OSS上传失败，请求ID: {request_id}: {e}", exc_info=True)
            return False
    
    async def store_report_url_to_cache(self, api_url: str, request_id: str) -> None:
        """
        存储报告URL到缓存
        
        Args:
            api_url: API URL
            request_id: 请求ID
            
        Raises:
            StorageError: 存储失败时抛出
        """
        try:
            redis_client = self._get_redis_client()
            redis_key = f"diagnosis:report_url:{request_id}"
            
            # 存储URL到Redis缓存（30天TTL）
            await asyncio.wait_for(
                redis_client.set_cache_async(redis_key, api_url, ttl_seconds=30*24*3600),
                timeout=self.redis_timeout
            )
            
            logger.info(f"报告URL已存储到缓存: {request_id} -> {api_url}")
            
        except asyncio.TimeoutError:
            raise StorageError(f"Redis存储超时，请求ID: {request_id}")
        except Exception as e:
            raise StorageError(f"Redis存储失败，请求ID: {request_id}: {e}")
    
    async def publish_report_ready_event(self, api_url: str, request_id: str) -> None:
        """
        发布报告就绪事件
        
        Args:
            api_url: API URL
            request_id: 请求ID
            
        Raises:
            StorageError: 发布失败时抛出
        """
        try:
            redis_client = self._get_redis_client()
            
            # 发布消息通知报告就绪
            channel = f"diagnosis:report_ready:{request_id}"
            message = json.dumps({
                "request_id": request_id,
                "url": api_url,
                "timestamp": datetime.now().isoformat(),
                "type": "report_ready"
            }, ensure_ascii=False)
            
            await asyncio.wait_for(
                redis_client.publish_async(channel, message),
                timeout=self.redis_timeout
            )
            
            logger.info(f"报告就绪事件已发布: {request_id}")
            
        except asyncio.TimeoutError:
            raise StorageError(f"事件发布超时，请求ID: {request_id}")
        except Exception as e:
            raise StorageError(f"事件发布失败，请求ID: {request_id}: {e}")
    
    def generate_api_url(self, request_id: str) -> str:
        """
        生成API URL
        
        Args:
            request_id: 请求ID
            
        Returns:
            str: API URL
        """
        return f"https://ecs-deep-diagnose.aliyun-inc.com/api/v1/reporter/{request_id}"
    
    async def upload_and_store_report(self, html_content: str, request_id: str) -> Optional[str]:
        """
        上传报告并存储相关信息
        
        Args:
            html_content: HTML内容
            request_id: 请求ID
            
        Returns:
            Optional[str]: API URL，失败时返回None
        """
        try:
            # 1. 上传到OSS
            upload_success = await self.upload_html_to_oss(html_content, request_id)
            
            if not upload_success:
                logger.error(f"OSS上传失败，请求ID: {request_id}")
                return None
            
            # 2. 生成API URL
            api_url = self.generate_api_url(request_id)
            
            # 3. 存储到缓存
            try:
                await self.store_report_url_to_cache(api_url, request_id)
            except StorageError as e:
                # 缓存存储失败不影响主流程，但记录错误
                logger.error(f"缓存存储失败（不影响主流程）: {e}")
            
            # 4. 发布事件
            try:
                await self.publish_report_ready_event(api_url, request_id)
            except StorageError as e:
                # 事件发布失败不影响主流程，但记录错误
                logger.error(f"事件发布失败（不影响主流程）: {e}")
            
            logger.info(f"报告上传和存储完成，请求ID: {request_id}")
            return api_url
            
        except Exception as e:
            logger.error(f"报告上传和存储失败，请求ID: {request_id}: {e}", exc_info=True)
            return None