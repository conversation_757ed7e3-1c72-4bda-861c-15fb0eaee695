#!/usr/bin/env python3
"""
HTML处理服务 - 从html_merger.py提取的HTML处理功能

负责HTML内容的清理、验证、模板生成等功能
"""

import logging
from datetime import datetime
from typing import Dict, Optional, Any
from jinja2 import Environment, FileSystemLoader
import os

logger = logging.getLogger(__name__)


class HtmlProcessingError(Exception):
    """HTML处理异常"""
    pass


class HtmlProcessingService:
    """HTML处理服务 - 提供HTML清理、验证和生成功能"""
    
    def __init__(self, template_dir: Optional[str] = None):
        self.default_title = "ECS诊断报告"
        self.section_titles = {
            "problem_cover_html": "封面页",
            "problem_description_html": "问题概述", 
            "diagnosis_info_html": "诊断分析",
            "key_findings_html": "关键发现",
            "evidence_chain_html": "支撑证据",
            "summary_conclusion_html": "总结建议"
        }
        
        # 初始化Jinja2模板环境
        if template_dir is None:
            # 默认模板目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            template_dir = os.path.join(current_dir, "../templates")
        
        self.template_env = Environment(
            loader=FileSystemLoader(template_dir),
            autoescape=True
        )
    
    def clean_content(self, content: str) -> str:
        """
        清理HTML内容
        
        Args:
            content: 原始HTML内容
            
        Returns:
            str: 清理后的HTML内容
        """
        if not content or not content.strip():
            return "<div>内容缺失</div>"
        
        content = content.strip()
        
        # 移除代码块标记
        if content.startswith(('```html', '```')):
            content = content[7:] if content.startswith('```html') else content[3:]
        if content.endswith('```'):
            content = content[:-3]
        
        return content.strip()
    
    def validate_html(self, html_content: str) -> bool:
        """
        验证HTML质量
        
        Args:
            html_content: HTML内容
            
        Returns:
            bool: 验证是否通过
        """
        if not html_content or len(html_content.strip()) < 100:
            return False
            
        required_tags = ["<html", "<head", "<body"]
        return all(tag in html_content.lower() for tag in required_tags)
    
    def extract_title_from_content(self, content: str) -> str:
        """
        从内容中提取标题
        
        Args:
            content: 内容文本
            
        Returns:
            str: 提取的标题
        """
        # 简单的标题提取逻辑，可以根据需要扩展
        if not content:
            return self.default_title
            
        # 查找可能的标题模式
        lines = content.split('\n')
        for line in lines[:10]:  # 只检查前10行
            line = line.strip()
            if line and len(line) < 200:  # 标题通常不会太长
                # 移除markdown标记
                line = line.lstrip('#').strip()
                if line:
                    return f"RCA 报告: {line}"
        
        return self.default_title
    
    def create_slideshow_report(self, sections: Dict[str, str], title: Optional[str] = None) -> str:
        """
        创建标准的幻灯片式HTML报告
        
        Args:
            sections: HTML片段字典
            title: 报告标题
            
        Returns:
            str: 完整的幻灯片式HTML报告
        """
        try:
            # 获取模板
            template = self.template_env.get_template("slideshow_report.html.j2")
            
            # 准备模板数据
            report_title = title or self.default_title
            slides = self._prepare_slide_data(sections, report_title)
            
            # 渲染模板
            html_content = template.render(
                title=report_title,
                slides=slides
            )
            
            return html_content
            
        except Exception as e:
            logger.error(f"使用Jinja2模板生成报告失败: {e}，回退到内置模板")
            # 如果模板失败，使用回退方案
            return self._generate_fallback_html(sections, title)
    
    def _prepare_slide_data(self, sections: Dict[str, str], report_title: str) -> list:
        """
        准备幻灯片数据
        
        Args:
            sections: HTML片段字典
            report_title: 报告标题
            
        Returns:
            list: 幻灯片数据列表
        """
        slides = []
        
        for key, section_title in self.section_titles.items():
            content = self.clean_content(sections.get(key, ""))
            
            # 封面页特殊处理
            if key == "problem_cover_html":
                if not content:
                    content = f'''
                    <div class="text-center">
                        <h1 class="text-4xl font-bold text-sky-400 mb-4">{report_title}</h1>
                        <p class="text-slate-400">ECS 深度诊断报告</p>
                    </div>'''
                
                slides.append({
                    'type': 'cover',
                    'title': section_title,
                    'content': content
                })
            else:
                slides.append({
                    'type': 'content',
                    'title': section_title,
                    'content': content
                })
        
        return slides
    
    def _generate_fallback_html(self, sections: Dict[str, str], title: Optional[str] = None) -> str:
        """
        生成回退HTML（当Jinja2模板不可用时）
        
        Args:
            sections: HTML片段字典
            title: 报告标题
            
        Returns:
            str: 完整的HTML报告
        """
        report_title = title or self.default_title
        
        # 生成6个独立的slide sections
        html_sections = []
        slide_counter = 1
        
        for key, section_title in self.section_titles.items():
            content = self.clean_content(sections.get(key, ""))
            
            # 为每个section生成独立的slide
            if key == "problem_cover_html":
                # 封面页特殊处理
                html_sections.append(f"""
    <!-- Slide {slide_counter}: {section_title} -->
    <section class="min-h-screen flex flex-col justify-center px-6 md:px-20 py-12 relative compact-section">
        <div class="space-y-4 flex-grow flex flex-col justify-center compact-spacing">
            {content or f'<div class="text-center"><h1 class="text-4xl font-bold text-sky-400 mb-4">{report_title}</h1><p class="text-slate-400">ECS 深度诊断报告</p></div>'}
        </div>
        <footer class="absolute bottom-8 right-8 text-sm text-slate-600">Slide {slide_counter} of 6</footer>
    </section>""")
            else:
                # 其他页面标准布局
                html_sections.append(f"""
    <!-- Slide {slide_counter}: {section_title} -->
    <section class="min-h-screen flex flex-col justify-center px-6 md:px-20 py-12 relative compact-section">
        <header class="pb-3 border-b border-slate-700 mb-4 compact-spacing">
            <h1 class="text-2xl font-bold text-sky-400">{section_title}</h1>
        </header>
        <div class="space-y-4 flex-grow flex flex-col justify-center compact-spacing">
            {content or f'<div class="text-slate-400">暂无{section_title}内容</div>'}
        </div>
        <footer class="absolute bottom-8 right-8 text-sm text-slate-600">Slide {slide_counter} of 6</footer>
    </section>""")
            
            slide_counter += 1
        
        return f"""<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{report_title}</title>
    <!-- 浏览器标签页图标（favicon） -->
    <link rel="shortcut icon" href="https://cloudbot2.aliyun-inc.com/cloudbot/img/favicon_front.ico">
    <!-- 可选的兼容写法 -->
    <link rel="icon" type="image/x-icon" href="https://cloudbot2.aliyun-inc.com/cloudbot/img/favicon_front.ico">
    <!-- 仅依赖此CDN脚本 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 为实现幻灯片滚动吸附效果，添加少量自定义样式 */
        html {{ scroll-behavior: smooth; }}
        body {{ scroll-snap-type: y mandatory; overflow-x: hidden; }}
        section {{ 
            scroll-snap-align: start; 
            min-height: 100vh; 
            width: 100%; 
            overflow-x: hidden;
            position: relative;
        }}
        .mono {{ font-family: 'SF Mono', 'Courier New', Courier, monospace; }}
        /* 防止内容溢出和重叠 */
        .section-content {{ 
            max-width: 100%; 
            overflow-wrap: break-word; 
            word-wrap: break-word;
        }}
        /* 紧凑布局样式 - 减少空余空间 */
        .compact-section {{
            padding-top: 1rem !important;
            padding-bottom: 1rem !important;
        }}
        .compact-spacing {{
            margin-bottom: 0.5rem !important;
        }}
        .compact-spacing-sm {{
            margin-bottom: 0.5rem !important;
        }}
        .tight-grid {{
            gap: 1rem !important;
        }}
        .compact-card {{
            padding: 1rem !important;
        }}
        
        /* 确保所有模块水平铺满 - 统一宽度 */
        section {{
            padding-left: 1.5rem !important;
            padding-right: 1.5rem !important;
        }}
        
        @media (min-width: 768px) {{
            section {{
                padding-left: 5rem !important;
                padding-right: 5rem !important;
            }}
        }}
        
        /* 移除嵌套section的额外padding和高度 */
        section section {{
            padding-top: 0 !important;
            padding-bottom: 0 !important;
            min-height: auto !important;
            padding-left: 0 !important;
            padding-right: 0 !important;
        }}
        
        /* 减少间距 */
        .space-y-6 > * + * {{
            margin-top: 1.5rem !important;
        }}
    </style>
</head>
<body class="bg-slate-900 text-slate-300 font-sans antialiased">

{''.join(html_sections)}

</body>
</html>"""
    
    # 保持向后兼容性的别名方法
    def generate_fallback_report(self, sections: Dict[str, str], title: Optional[str] = None) -> str:
        """
        [已废弃] 请使用 create_slideshow_report 方法
        
        这是为了向后兼容性保留的方法，内部调用新的 create_slideshow_report 方法
        """
        logger.warning("generate_fallback_report 方法已废弃，请使用 create_slideshow_report")
        return self.create_slideshow_report(sections, title)

    def analyze_content_complexity(self, sections: Dict[str, str]) -> Dict[str, Any]:
        """
        分析内容复杂度
        
        Args:
            sections: HTML片段字典
            
        Returns:
            dict: 复杂度分析结果
        """
        total_length = sum(len(content) for content in sections.values())
        
        # 检查是否有复杂的HTML结构
        complex_patterns = ['<table', '<ul', '<ol', '<div class=', '<span class=']
        has_complex_html = False
        complex_sections = []
        
        for key, content in sections.items():
            content_lower = content.lower()
            section_complex = any(pattern in content_lower for pattern in complex_patterns)
            if section_complex or len(content) > 1000:
                has_complex_html = True
                complex_sections.append(key)
        
        return {
            'total_length': total_length,
            'has_complex_html': has_complex_html,
            'complex_sections': complex_sections,
            'section_count': len(sections),
            'avg_section_length': total_length / max(len(sections), 1)
        }
    
    def should_use_llm_merge(self, complexity_analysis: Dict[str, Any]) -> bool:
        """
        判断是否应该使用LLM合并
        
        Args:
            complexity_analysis: 复杂度分析结果
            
        Returns:
            bool: 是否使用LLM合并
        """
        # 提高阈值，更多情况下使用规则合并（更稳定）
        if complexity_analysis['total_length'] < 500:
            return False
            
        if not complexity_analysis['has_complex_html']:
            return False
            
        # 如果平均section长度太小，也使用规则合并
        if complexity_analysis['avg_section_length'] < 200:
            return False
            
        return True