#!/usr/bin/env python3
"""
报告生成服务 - 从html_merger.py提取的报告生成功能

负责智能合并HTML片段、LLM调用、性能数据合并等核心业务逻辑
"""

import asyncio
import logging
from typing import Dict, Optional, Any

from deep_diagnose.core.reasoning.workflow.types import ReasoningState
from deep_diagnose.llms.agent_llm_config import AGENT_LLM_MAP
from deep_diagnose.llms.llm import get_llm_by_type
from deep_diagnose.prompts.template import apply_prompt_template
from .html_processing_service import HtmlProcessingService
from ..performance_service import create_vm_performance_service
from ..html_service import HtmlService

logger = logging.getLogger(__name__)


class ReportGenerationError(Exception):
    """报告生成异常"""
    pass


class ReportGenerationService:
    """报告生成服务 - 处理HTML报告的智能生成"""
    
    def __init__(self, 
                 html_processing_service: Optional[HtmlProcessingService] = None,
                 performance_service = None,
                 html_service: Optional[HtmlService] = None):
        """
        初始化报告生成服务
        
        Args:
            html_processing_service: HTML处理服务
            performance_service: 性能服务
            html_service: HTML服务
        """
        self.html_processing_service = html_processing_service or HtmlProcessingService()
        
        # 性能服务依赖注入
        if performance_service is None:
            self.performance_service = create_vm_performance_service()
        else:
            self.performance_service = performance_service
            
        self.html_service = html_service or HtmlService()
        
        # LLM重试配置
        self.llm_retry_timeouts = [600.0, 240.0, 180.0]  # 10分钟 -> 4分钟 -> 3分钟
    
    def extract_sections_from_state(self, state: ReasoningState) -> Dict[str, str]:
        """
        从状态中提取HTML片段
        
        Args:
            state: 推理状态
            
        Returns:
            Dict[str, str]: HTML片段字典
        """
        section_keys = [
            "problem_cover_html", "problem_description_html", "diagnosis_info_html", 
            "key_findings_html", "evidence_chain_html", "summary_conclusion_html"
        ]
        return {key: state.get(key, "") for key in section_keys}
    
    def extract_user_query_from_state(self, state: ReasoningState) -> str:
        """
        从状态中提取用户查询
        
        Args:
            state: 推理状态
            
        Returns:
            str: 用户查询内容
        """
        messages = state.get("messages", [])
        for msg in messages:
            if hasattr(msg, "content") and msg.content:
                return msg.content
            elif isinstance(msg, str):
                return msg
        return ""
    
    async def generate_complete_report(self, 
                                     sections: Dict[str, str], 
                                     state: ReasoningState, 
                                     user_query: str) -> str:
        """
        生成完整的HTML报告
        
        Args:
            sections: HTML片段
            state: 推理状态
            user_query: 用户查询
            
        Returns:
            str: 完整的HTML报告
            
        Raises:
            ReportGenerationError: 生成失败时抛出
        """
        try:
            # 1. 分析内容复杂度
            complexity_analysis = self.html_processing_service.analyze_content_complexity(sections)
            logger.info(f"内容复杂度分析: {complexity_analysis}")
            
            # 2. 选择生成策略
            if self.html_processing_service.should_use_llm_merge(complexity_analysis):
                logger.info("使用LLM智能合并策略")
                try:
                    return await self._llm_merge_with_retry(sections, state, user_query)
                except Exception as e:
                    logger.warning(f"LLM合并失败，回退到规则合并: {e}")
                    return self._fallback_merge(sections, state, user_query)
            else:
                logger.info("使用规则合并策略")
                return self._fallback_merge(sections, state, user_query)
                
        except Exception as e:
            logger.error(f"报告生成失败: {e}", exc_info=True)
            raise ReportGenerationError(f"报告生成失败: {e}")
    
    def _fallback_merge(self, 
                       sections: Dict[str, str], 
                       state: ReasoningState, 
                       user_query: str) -> str:
        """
        回退合并策略 - 使用规则生成
        
        Args:
            sections: HTML片段
            state: 推理状态
            user_query: 用户查询
            
        Returns:
            str: 完整的HTML报告
        """
        # 尝试从final_report提取标题
        final_report = state.get("final_report", "")
        title = self.html_processing_service.extract_title_from_content(
            final_report or user_query
        )
        
        return self.html_processing_service.generate_fallback_report(sections, title)
    
    async def _llm_merge_with_retry(self, 
                                   sections: Dict[str, str], 
                                   state: ReasoningState, 
                                   user_query: str) -> str:
        """
        带重试机制的LLM合并
        
        Args:
            sections: HTML片段
            state: 推理状态  
            user_query: 用户查询
            
        Returns:
            str: LLM生成的HTML报告
            
        Raises:
            ReportGenerationError: 所有重试都失败时抛出
        """
        # 构建LLM调用上下文
        context = self._build_llm_context(sections, state, user_query)
        
        # 获取LLM实例
        llm = get_llm_by_type(AGENT_LLM_MAP.get("html_merger", "basic"))
        messages = apply_prompt_template("agent_html_report_merger", context)
        
        last_error = None
        
        # 多次重试，逐步降低超时时间
        for attempt, timeout in enumerate(self.llm_retry_timeouts, 1):
            try:
                logger.info(f"LLM调用尝试 {attempt}/{len(self.llm_retry_timeouts)}，超时: {timeout}秒")
                
                # 配置LLM重试参数
                llm_with_config = llm.with_config({
                    "max_retries": 2,
                    "retry_delay": 1.0
                })
                
                response = await asyncio.wait_for(
                    llm_with_config.ainvoke(messages),
                    timeout=timeout
                )
                
                merged_html = getattr(response, "content", "")
                if not merged_html or not merged_html.strip():
                    raise ValueError("LLM返回空响应")
                
                # 清理和验证LLM输出
                content = self.html_processing_service.clean_content(merged_html)
                if not content.startswith("<!DOCTYPE") and content.startswith("<html"):
                    content = "<!DOCTYPE html>\n" + content
                
                logger.info(f"LLM调用成功，尝试次数: {attempt}")
                return content
                
            except asyncio.TimeoutError as e:
                last_error = e
                logger.warning(f"LLM调用超时 (尝试 {attempt}/{len(self.llm_retry_timeouts)})，超时时间: {timeout}秒")
                if attempt < len(self.llm_retry_timeouts):
                    await asyncio.sleep(2)  # 重试前等待2秒
                continue
                
            except asyncio.CancelledError as e:
                last_error = e
                logger.warning(f"LLM调用被取消 (尝试 {attempt}/{len(self.llm_retry_timeouts)})")
                if attempt < len(self.llm_retry_timeouts):
                    await asyncio.sleep(2)
                continue
                
            except Exception as e:
                last_error = e
                logger.error(f"LLM调用失败 (尝试 {attempt}/{len(self.llm_retry_timeouts)}): {e}")
                if attempt < len(self.llm_retry_timeouts):
                    await asyncio.sleep(2)
                continue
        
        # 所有重试都失败了
        error_msg = f"LLM调用所有重试都失败，最后错误: {last_error}"
        logger.error(error_msg)
        raise ReportGenerationError(error_msg)
    
    def _build_llm_context(self, 
                          sections: Dict[str, str], 
                          state: ReasoningState, 
                          user_query: str) -> Dict[str, Any]:
        """
        构建LLM调用上下文
        
        Args:
            sections: HTML片段
            state: 推理状态
            user_query: 用户查询
            
        Returns:
            Dict[str, Any]: LLM上下文
        """
        context = {
            "user_query": user_query,
            "final_report": state.get("final_report", ""),
            "observations": state.get("observations", []),
            "current_plan": state.get("current_plan", ""),
            "messages": []
        }
        
        # 清理HTML片段并添加到上下文
        for key, content in sections.items():
            context[key] = self.html_processing_service.clean_content(content)
        
        return context
    
    async def merge_performance_data(self, html_content: str, request_id: str) -> str:
        """
        合并性能数据到HTML报告中
        
        Args:
            html_content: 原始HTML内容
            request_id: 请求ID
            
        Returns:
            str: 合并性能数据后的HTML内容
        """
        try:
            # 使用性能服务生成性能数据section
            performance_section = await self.performance_service.generate_report(request_id)
            
            if not performance_section:
                logger.info(f"未找到性能数据，跳过性能section合并，请求ID: {request_id}")
                return html_content
            
            # 使用HTML服务进行插入优化
            enhanced_html = await self.html_service.insert_performance_section(
                html_content, performance_section, request_id
            )
            
            # 分析插入结果（调试用）
            if logger.isEnabledFor(logging.DEBUG):
                analysis = await self.html_service.analyze_html_structure(enhanced_html)
                logger.debug(f"HTML结构分析结果: {analysis}")
            
            return enhanced_html
            
        except Exception as e:
            logger.error(f"合并性能数据失败，请求ID: {request_id}: {e}", exc_info=True)
            # 如果合并失败，返回原始HTML
            return html_content