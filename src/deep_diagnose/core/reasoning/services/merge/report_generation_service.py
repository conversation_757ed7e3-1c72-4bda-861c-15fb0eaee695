#!/usr/bin/env python3
"""
优化的报告生成服务 - 使用策略模式重构合并逻辑

主要改进:
1. 统一接口：LLM合并和规则合并实现同一个接口
2. 策略模式：清晰分离不同的合并策略
3. 提高可读性：更清晰的代码结构和职责分离
4. 增强扩展性：便于添加新的合并策略
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, Optional, Any
from enum import Enum

from deep_diagnose.core.reasoning.workflow.types import ReasoningState
from deep_diagnose.llms.agent_llm_config import AGENT_LLM_MAP
from deep_diagnose.llms.llm import get_llm_by_type
from deep_diagnose.prompts.template import apply_prompt_template
from deep_diagnose.common.config import get_config
from .html_processing_service import HtmlProcessingService
from ..performance_service import create_vm_performance_service
from ..html_service import HtmlService

logger = logging.getLogger(__name__)


class MergeStrategy(Enum):
    """合并策略枚举"""
    LLM = "llm"
    RULE_BASED = "rule_based"


class ReportGenerationError(Exception):
    """报告生成异常"""
    pass


class MergeContext:
    """合并上下文 - 封装合并所需的所有数据"""
    
    def __init__(self, 
                 sections: Dict[str, str], 
                 state: ReasoningState, 
                 user_query: str,
                 html_processing_service: HtmlProcessingService):
        self.sections = sections
        self.state = state
        self.user_query = user_query
        self.html_processing_service = html_processing_service
        
    @property
    def title(self) -> str:
        """提取报告标题"""
        final_report = self.state.get("final_report", "")
        return self.html_processing_service.extract_title_from_content(
            final_report or self.user_query
        )
    
    @property
    def llm_context(self) -> Dict[str, Any]:
        """构建LLM调用上下文"""
        context = {
            "user_query": self.user_query,
            "final_report": self.state.get("final_report", ""),
            "observations": self.state.get("observations", []),
            "current_plan": self.state.get("current_plan", ""),
            "messages": []
        }
        
        # 清理HTML片段并添加到上下文
        for key, content in self.sections.items():
            context[key] = self.html_processing_service.clean_content(content)
        
        return context


class ReportMerger(ABC):
    """报告合并器抽象基类 - 统一接口"""
    
    @abstractmethod
    async def merge(self, context: MergeContext) -> str:
        """
        合并HTML片段生成完整报告
        
        Args:
            context: 合并上下文
            
        Returns:
            str: 完整的HTML报告
            
        Raises:
            ReportGenerationError: 合并失败时抛出
        """
        pass
    
    @property
    @abstractmethod
    def strategy_name(self) -> str:
        """策略名称"""
        pass


class RuleBasedMerger(ReportMerger):
    """基于规则的合并器 - 快速可靠的回退策略"""
    
    async def merge(self, context: MergeContext) -> str:
        """
        使用规则生成HTML报告
        
        Args:
            context: 合并上下文
            
        Returns:
            str: 规则生成的HTML报告
        """
        logger.info(f"使用{self.strategy_name}合并策略")
        
        try:
            html_report = context.html_processing_service.create_slideshow_report(
                context.sections, 
                context.title
            )
            
            if not html_report or not html_report.strip():
                raise ReportGenerationError("规则合并生成空报告")
            
            logger.info(f"{self.strategy_name}合并成功")
            return html_report
            
        except Exception as e:
            error_msg = f"{self.strategy_name}合并失败: {e}"
            logger.error(error_msg, exc_info=True)
            raise ReportGenerationError(error_msg)
    
    @property
    def strategy_name(self) -> str:
        return "规则合并"


class LLMMerger(ReportMerger):
    """基于LLM的智能合并器 - 高质量但可能失败的策略"""
    
    def __init__(self, retry_timeouts: Optional[list] = None):
        """
        初始化LLM合并器
        
        Args:
            retry_timeouts: 重试超时时间列表，默认为[600.0, 240.0, 180.0]
        """
        self.retry_timeouts = retry_timeouts or [600.0, 240.0, 180.0]
    
    async def merge(self, context: MergeContext) -> str:
        """
        使用LLM智能合并HTML片段
        
        Args:
            context: 合并上下文
            
        Returns:
            str: LLM生成的HTML报告
            
        Raises:
            ReportGenerationError: 所有重试都失败时抛出
        """
        logger.info(f"使用{self.strategy_name}合并策略")
        
        # 获取LLM实例和消息
        llm = get_llm_by_type(AGENT_LLM_MAP.get("html_merger", "basic"))
        messages = apply_prompt_template("agent_html_report_merger", context.llm_context)
        
        # 执行带重试的LLM调用
        html_content = await self._invoke_llm_with_retry(llm, messages)
        
        # 后处理LLM输出
        processed_content = self._post_process_llm_output(html_content, context)
        
        logger.info(f"{self.strategy_name}合并成功")
        return processed_content
    
    async def _invoke_llm_with_retry(self, llm, messages) -> str:
        """
        带重试机制的LLM调用
        
        Args:
            llm: LLM实例
            messages: 消息列表
            
        Returns:
            str: LLM响应内容
            
        Raises:
            ReportGenerationError: 所有重试都失败时抛出
        """
        last_error = None
        
        # 多次重试，逐步降低超时时间
        for attempt, timeout in enumerate(self.retry_timeouts, 1):
            try:
                logger.info(f"LLM调用尝试 {attempt}/{len(self.retry_timeouts)}，超时: {timeout}秒")
                
                # 配置LLM重试参数
                llm_with_config = llm.with_config({
                    "max_retries": 2,
                    "retry_delay": 1.0
                })
                
                response = await asyncio.wait_for(
                    llm_with_config.ainvoke(messages),
                    timeout=timeout
                )
                
                merged_html = getattr(response, "content", "")
                if not merged_html or not merged_html.strip():
                    raise ValueError("LLM返回空响应")
                
                logger.info(f"LLM调用成功，尝试次数: {attempt}")
                return merged_html
                
            except asyncio.TimeoutError as e:
                last_error = e
                logger.warning(f"LLM调用超时 (尝试 {attempt}/{len(self.retry_timeouts)})，超时时间: {timeout}秒")
                
            except asyncio.CancelledError as e:
                last_error = e
                logger.warning(f"LLM调用被取消 (尝试 {attempt}/{len(self.retry_timeouts)})")
                
            except Exception as e:
                last_error = e
                logger.error(f"LLM调用失败 (尝试 {attempt}/{len(self.retry_timeouts)}): {e}")
            
            # 重试前等待
            if attempt < len(self.retry_timeouts):
                await asyncio.sleep(2)
        
        # 所有重试都失败了
        error_msg = f"LLM调用所有重试都失败，最后错误: {last_error}"
        logger.error(error_msg)
        raise ReportGenerationError(error_msg)
    
    def _post_process_llm_output(self, content: str, context: MergeContext) -> str:
        """
        后处理LLM输出
        
        Args:
            content: LLM原始输出
            context: 合并上下文
            
        Returns:
            str: 处理后的HTML内容
        """
        # 清理和验证LLM输出
        cleaned_content = context.html_processing_service.clean_content(content)
        
        # 确保有DOCTYPE声明
        if not cleaned_content.startswith("<!DOCTYPE") and cleaned_content.startswith("<html"):
            cleaned_content = "<!DOCTYPE html>\n" + cleaned_content
        
        return cleaned_content
    
    @property
    def strategy_name(self) -> str:
        return "LLM智能合并"


class MergerFactory:
    """合并器工厂 - 负责创建不同的合并器实例"""
    
    @staticmethod
    def create_merger(strategy: Optional[MergeStrategy] = None, **kwargs) -> ReportMerger:
        """
        创建合并器实例
        
        Args:
            strategy: 合并策略，如果为None则从配置文件读取
            **kwargs: 额外参数
            
        Returns:
            ReportMerger: 合并器实例
            
        Raises:
            ValueError: 不支持的策略时抛出
        """
        # 如果没有指定策略，从配置文件读取
        if strategy is None:
            strategy = MergerFactory._get_strategy_from_config()
        
        if strategy == MergeStrategy.LLM:
            return LLMMerger(retry_timeouts=kwargs.get('retry_timeouts'))
        elif strategy == MergeStrategy.RULE_BASED:
            return RuleBasedMerger()
        else:
            raise ValueError(f"不支持的合并策略: {strategy}")
    
    @staticmethod
    def _get_strategy_from_config() -> MergeStrategy:
        """
        从配置文件获取合并策略
        
        Returns:
            MergeStrategy: 配置的合并策略
        """
        try:
            config = get_config()
            merge_method = config.reasoning.reporting.merge_method
            
            if merge_method == "llm":
                return MergeStrategy.LLM
            elif merge_method == "rule":
                return MergeStrategy.RULE_BASED
            else:
                logger.warning(f"未知的合并方法配置: {merge_method}，使用默认规则合并")
                return MergeStrategy.RULE_BASED
        except Exception as e:
            logger.warning(f"读取合并策略配置失败: {e}，使用默认规则合并")
            return MergeStrategy.RULE_BASED


class ReportGenerationService:
    """优化的报告生成服务 - 使用策略模式"""
    
    def __init__(self, 
                 html_processing_service: Optional[HtmlProcessingService] = None,
                 performance_service = None,
                 html_service: Optional[HtmlService] = None,
                 llm_retry_timeouts: Optional[list] = None):
        """
        初始化报告生成服务
        
        Args:
            html_processing_service: HTML处理服务
            performance_service: 性能服务
            html_service: HTML服务
            llm_retry_timeouts: LLM重试超时时间列表
        """
        self.html_processing_service = html_processing_service or HtmlProcessingService()
        
        # 性能服务依赖注入
        if performance_service is None:
            self.performance_service = create_vm_performance_service()
        else:
            self.performance_service = performance_service
            
        self.html_service = html_service or HtmlService()
        
        # 配置合并器
        self.llm_retry_timeouts = llm_retry_timeouts or [600.0, 240.0, 180.0]
        self._setup_mergers()
    
    def _setup_mergers(self):
        """设置合并器实例"""
        # 根据配置创建主要合并器
        self.primary_merger = MergerFactory.create_merger(
            retry_timeouts=self.llm_retry_timeouts
        )
        
        # 总是准备规则合并器作为回退
        self.fallback_merger = MergerFactory.create_merger(
            MergeStrategy.RULE_BASED
        )
    
    def extract_sections_from_state(self, state: ReasoningState) -> Dict[str, str]:
        """
        从状态中提取HTML片段
        
        Args:
            state: 推理状态
            
        Returns:
            Dict[str, str]: HTML片段字典
        """
        section_keys = [
            "problem_cover_html", "problem_description_html", "diagnosis_info_html", 
            "key_findings_html", "evidence_chain_html", "summary_conclusion_html"
        ]
        return {key: state.get(key, "") for key in section_keys}
    
    def extract_user_query_from_state(self, state: ReasoningState) -> str:
        """
        从状态中提取用户查询
        
        Args:
            state: 推理状态
            
        Returns:
            str: 用户查询内容
        """
        messages = state.get("messages", [])
        for msg in messages:
            if hasattr(msg, "content") and msg.content:
                return msg.content
            elif isinstance(msg, str):
                return msg
        return ""
    
    def _get_configured_strategy(self) -> MergeStrategy:
        """
        获取配置的合并策略
        
        Returns:
            MergeStrategy: 配置的合并策略
        """
        return MergerFactory._get_strategy_from_config()
    
    async def generate_complete_report(self, 
                                     sections: Dict[str, str], 
                                     state: ReasoningState, 
                                     user_query: str) -> str:
        """
        生成完整的HTML报告
        
        Args:
            sections: HTML片段
            state: 推理状态
            user_query: 用户查询
            
        Returns:
            str: 完整的HTML报告
            
        Raises:
            ReportGenerationError: 生成失败时抛出
        """
        try:
            # 创建合并上下文
            context = MergeContext(sections, state, user_query, self.html_processing_service)
            
            # 执行合并（策略已在初始化时根据配置确定）
            return await self._execute_merge_with_fallback(context)
            
        except Exception as e:
            logger.error(f"报告生成失败: {e}", exc_info=True)
            raise ReportGenerationError(f"报告生成失败: {e}")
    
    async def _execute_merge_with_fallback(self, context: MergeContext) -> str:
        """
        执行合并策略，支持自动回退
        
        Args:
            context: 合并上下文
            
        Returns:
            str: 合并后的HTML报告
        """
        # 优先使用配置的主要策略
        try:
            result = await self.primary_merger.merge(context)
            logger.info(f"使用{self.primary_merger.strategy_name}成功生成报告")
            return result
        except Exception as e:
            # 如果主要策略失败且不是规则合并，则回退到规则合并
            if isinstance(self.primary_merger, LLMMerger):
                logger.warning(f"{self.primary_merger.strategy_name}失败，回退到{self.fallback_merger.strategy_name}: {e}")
                return await self.fallback_merger.merge(context)
            else:
                # 如果规则合并也失败了，重新抛出异常
                raise
    
    async def merge_performance_data(self, html_content: str, request_id: str) -> str:
        """
        合并性能数据到HTML报告中
        
        Args:
            html_content: 原始HTML内容
            request_id: 请求ID
            
        Returns:
            str: 合并性能数据后的HTML内容
        """
        try:
            # 使用性能服务生成性能数据section
            performance_section = await self.performance_service.generate_report(request_id)
            
            if not performance_section:
                logger.info(f"未找到性能数据，跳过性能section合并，请求ID: {request_id}")
                return html_content
            
            # 使用HTML服务进行插入优化
            enhanced_html = await self.html_service.insert_performance_section(
                html_content, performance_section, request_id
            )
            
            # 分析插入结果（调试用）
            if logger.isEnabledFor(logging.DEBUG):
                analysis = await self.html_service.analyze_html_structure(enhanced_html)
                logger.debug(f"HTML结构分析结果: {analysis}")
            
            return enhanced_html
            
        except Exception as e:
            logger.error(f"合并性能数据失败，请求ID: {request_id}: {e}", exc_info=True)
            # 如果合并失败，返回原始HTML
            return html_content