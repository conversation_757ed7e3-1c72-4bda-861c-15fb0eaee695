<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <!-- 浏览器标签页图标（favicon） -->
    <link rel="shortcut icon" href="https://cloudbot2.aliyun-inc.com/cloudbot/img/favicon_front.ico">
    <!-- 可选的兼容写法 -->
    <link rel="icon" type="image/x-icon" href="https://cloudbot2.aliyun-inc.com/cloudbot/img/favicon_front.ico">
    <!-- 仅依赖此CDN脚本 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 基础幻灯片样式 */
        html { scroll-behavior: smooth; }
        body { 
            overflow-x: hidden;
            margin: 0;
            padding: 0;
        }
        section { 
            min-height: 100vh; 
            width: 100%; 
            overflow-x: hidden;
            position: relative;
        }
        .mono { font-family: 'SF Mono', 'Courier New', Courier, monospace; }
        
        /* 防止内容溢出 */
        .section-content { 
            max-width: 100%; 
            overflow-wrap: break-word; 
            word-wrap: break-word;
        }
        
        /* 紧凑布局样式 */
        .compact-section {
            padding-top: 1rem !important;
            padding-bottom: 1rem !important;
        }
        .compact-spacing {
            margin-bottom: 0.5rem !important;
        }
        
        /* 统一宽度 */
        section {
            padding-left: 1.5rem !important;
            padding-right: 1.5rem !important;
        }
        
        @media (min-width: 768px) {
            section {
                padding-left: 5rem !important;
                padding-right: 5rem !important;
            }
        }
        
        /* 移除嵌套section的额外padding */
        section section {
            padding-top: 0 !important;
            padding-bottom: 0 !important;
            min-height: auto !important;
            padding-left: 0 !important;
            padding-right: 0 !important;
        }
        
        /* 减少间距 */
        .space-y-6 > * + * {
            margin-top: 1.5rem !important;
        }
        
        /* 进度条 */
        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #38b2ac, #4fd1c7);
            z-index: 1001;
            transition: width 0.3s ease;
        }
        
        /* 虚化的控制栏 */
        .slide-controls {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            display: flex;
            gap: 16px;
            background: rgba(15, 23, 42, 0.4);
            backdrop-filter: blur(20px);
            padding: 12px 20px;
            border-radius: 40px;
            border: 1px solid rgba(56, 178, 172, 0.15);
            box-shadow: 0 4px 30px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            opacity: 0.7;
        }
        
        .slide-controls:hover {
            background: rgba(15, 23, 42, 0.8);
            border-color: rgba(56, 178, 172, 0.3);
            opacity: 1;
            transform: translateX(-50%) translateY(-2px);
            box-shadow: 0 6px 40px rgba(0, 0, 0, 0.3);
        }
        
        .control-btn {
            background: rgba(56, 178, 172, 0.1);
            border: 1px solid rgba(56, 178, 172, 0.2);
            color: rgba(56, 178, 172, 0.9);
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            backdrop-filter: blur(10px);
        }
        
        .control-btn:hover {
            background: rgba(56, 178, 172, 0.25);
            border-color: rgba(56, 178, 172, 0.6);
            color: #4fd1c7;
            transform: scale(1.05);
        }
        
        .control-btn:disabled {
            opacity: 0.3;
            cursor: not-allowed;
            transform: none;
        }
        
        .control-btn:disabled:hover {
            background: rgba(56, 178, 172, 0.1);
            border-color: rgba(56, 178, 172, 0.2);
            color: rgba(56, 178, 172, 0.9);
            transform: none;
        }
        
        .slide-indicator {
            background: transparent;
            border: none;
            color: rgba(100, 116, 139, 0.8);
            font-size: 13px;
            font-weight: 500;
            cursor: default;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 0 12px;
        }
        
        /* 虚化的点导航 */
        .slide-dots {
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            display: flex;
            gap: 8px;
            opacity: 0.6;
            transition: opacity 0.3s ease;
        }
        
        .slide-dots:hover {
            opacity: 1;
        }
        
        .slide-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(100, 116, 139, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }
        
        .slide-dot.active {
            background: rgba(56, 178, 172, 0.8);
            transform: scale(1.3);
            box-shadow: 0 0 10px rgba(56, 178, 172, 0.4);
        }
        
        .slide-dot:hover {
            background: rgba(79, 209, 199, 0.7);
            transform: scale(1.1);
        }
        
        /* 虚化的键盘提示 */
        .keyboard-hint {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(15px);
            color: rgba(148, 163, 184, 0.8);
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            border: 1px solid rgba(100, 116, 139, 0.2);
            opacity: 0.5;
            transition: all 0.3s ease;
        }
        
        .keyboard-hint:hover {
            opacity: 0.9;
            background: rgba(30, 41, 59, 0.7);
            border-color: rgba(100, 116, 139, 0.4);
            color: rgba(148, 163, 184, 1);
        }
        
        /* 隐藏所有幻灯片，只显示当前的 */
        .slide {
            display: none;
        }
        
        .slide.active {
            display: flex;
        }
    </style>
</head>
<body class="bg-slate-900 text-slate-300 font-sans antialiased">

    <!-- 进度条 -->
    <div class="progress-bar" id="progressBar"></div>

    <!-- 键盘导航提示 -->
    <div class="keyboard-hint">
        <span>← → 切换幻灯片</span>
    </div>

    <!-- 简化的幻灯片控制栏 -->
    <div class="slide-controls">
        <button class="control-btn" id="prevBtn">
            <span>◀</span> 上一页
        </button>
        <div class="slide-indicator">
            <span>第 <span id="currentSlide">1</span> 页 / 共 <span id="totalSlides">{{ slides | length }}</span> 页</span>
        </div>
        <button class="control-btn" id="nextBtn">
            下一页 <span>▶</span>
        </button>
    </div>

    <!-- 幻灯片点导航 -->
    <div class="slide-dots">
        {% for slide in slides %}
        <div class="slide-dot{% if loop.index == 1 %} active{% endif %}" data-slide="{{ loop.index - 1 }}"></div>
        {% endfor %}
    </div>

{% for slide in slides %}
    <!-- Slide {{ loop.index }}: {{ slide.title }} -->
    <section class="slide min-h-screen flex-col {% if loop.index == 1 %}justify-center px-6 md:px-20 py-12 pb-32{% else %}justify-start px-6 md:px-20 pt-2 pb-32{% endif %} relative compact-section{% if loop.index == 1 %} active{% endif %}" data-slide="{{ loop.index - 1 }}">
        {% if slide.type == 'cover' %}
            <div class="space-y-4 flex-grow flex flex-col justify-center compact-spacing">
                {{ slide.content | safe }}
            </div>
        {% else %}
            <div class="space-y-4 flex-grow flex flex-col justify-start compact-spacing">
                {% if slide.content %}
                    {{ slide.content | safe }}
                {% else %}
                    <div class="text-slate-400">暂无{{ slide.title }}内容</div>
                {% endif %}
            </div>
        {% endif %}
    </section>
{% endfor %}

<script>
(function() {
    let currentSlideIndex = 0;
    let slides = document.querySelectorAll('.slide');
    let totalSlides = slides.length;
    
    console.log('总幻灯片数:', totalSlides);
    
    // 更新幻灯片显示
    function updateSlideDisplay() {
        console.log('更新显示，当前索引:', currentSlideIndex);
        
        // 更新页码显示
        document.getElementById('currentSlide').textContent = currentSlideIndex + 1;
        
        // 更新进度条
        const progressPercent = ((currentSlideIndex + 1) / totalSlides) * 100;
        document.getElementById('progressBar').style.width = progressPercent + '%';
        
        // 隐藏所有幻灯片，显示当前幻灯片
        slides.forEach((slide, index) => {
            if (index === currentSlideIndex) {
                slide.classList.add('active');
            } else {
                slide.classList.remove('active');
            }
        });
        
        // 更新点导航
        document.querySelectorAll('.slide-dot').forEach((dot, index) => {
            if (index === currentSlideIndex) {
                dot.classList.add('active');
            } else {
                dot.classList.remove('active');
            }
        });
        
        // 更新按钮状态
        document.getElementById('prevBtn').disabled = currentSlideIndex === 0;
        document.getElementById('nextBtn').disabled = currentSlideIndex === totalSlides - 1;
    }
    
    // 下一张幻灯片
    function nextSlide() {
        console.log('点击下一页，当前索引:', currentSlideIndex);
        if (currentSlideIndex < totalSlides - 1) {
            currentSlideIndex++;
            updateSlideDisplay();
        }
    }
    
    // 上一张幻灯片
    function prevSlide() {
        console.log('点击上一页，当前索引:', currentSlideIndex);
        if (currentSlideIndex > 0) {
            currentSlideIndex--;
            updateSlideDisplay();
        }
    }
    
    // 跳转到指定幻灯片
    function goToSlide(index) {
        console.log('跳转到页面:', index);
        if (index >= 0 && index < totalSlides) {
            currentSlideIndex = index;
            updateSlideDisplay();
        }
    }
    
    // 事件监听器
    document.getElementById('nextBtn').addEventListener('click', function(e) {
        e.preventDefault();
        console.log('下一页按钮被点击');
        nextSlide();
    });
    
    document.getElementById('prevBtn').addEventListener('click', function(e) {
        e.preventDefault();
        console.log('上一页按钮被点击');
        prevSlide();
    });
    
    // 点导航事件
    document.querySelectorAll('.slide-dot').forEach((dot, index) => {
        dot.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('点导航被点击，索引:', index);
            goToSlide(index);
        });
    });
    
    // 键盘导航
    document.addEventListener('keydown', (e) => {
        switch(e.key) {
            case 'ArrowLeft':
            case 'ArrowUp':
                e.preventDefault();
                prevSlide();
                break;
            case 'ArrowRight':
            case 'ArrowDown':
                e.preventDefault();
                nextSlide();
                break;
            case 'Home':
                e.preventDefault();
                goToSlide(0);
                break;
            case 'End':
                e.preventDefault();
                goToSlide(totalSlides - 1);
                break;
        }
    });
    
    // 初始化
    console.log('初始化幻灯片');
    updateSlideDisplay();
    
})();
</script>

</body>
</html>