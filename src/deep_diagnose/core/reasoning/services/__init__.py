"""
服务层模块

提供各种业务服务的抽象和实现，包括：
- 性能数据服务 (performance_service.py)
- HTML处理服务 (html_service.py) 
- HTML报告合并服务包 (merge/)
  - HTML处理服务 (merge/html_processing_service.py)
  - 存储服务 (merge/storage_service.py)
  - 报告生成服务 (merge/report_generation_service.py)
"""

# 重新导出merge包中的服务，保持向后兼容
from .merge import (
    HtmlProcessingService,
    StorageService,
    ReportGenerationService
)

__all__ = [
    'HtmlProcessingService',
    'StorageService', 
    'ReportGenerationService'
]