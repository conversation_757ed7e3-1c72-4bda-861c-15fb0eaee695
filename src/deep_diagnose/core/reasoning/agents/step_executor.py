"""
步骤执行服务 - 专门处理计划步骤的执行逻辑
支持串行和并发执行模式
"""

import asyncio
import logging
from typing import List, Tuple
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.types import Command

from deep_diagnose.core.reasoning.workflow.types import ReasoningState
from .react_agent_factory import setup_mcp_tools, create_agent_with_tools, get_recursion_limit
from deep_diagnose.prompts.planner_model import StepStatus, Step, StepType
from deep_diagnose.common.config import get_config

logger = logging.getLogger(__name__)


class StepExecutor:
    """步骤执行器 - 负责执行计划中的具体步骤"""
    
    def __init__(self, agent_instance):
        self.agent = agent_instance
        self.logger = agent_instance.logger
    
    async def execute_current_step(self, state: ReasoningState, config: RunnableConfig) -> Command:
        """执行当前agent类型的步骤 - 支持同类型并发"""
        
        # 找到当前agent类型可执行的步骤
        ready_steps = self._find_ready_steps(state)
        
        if not ready_steps:
            return Command(goto="research_team")
        
        # 限制并发数量
        max_concurrent = self._get_max_concurrent_steps()
        steps_to_execute = ready_steps[:max_concurrent]
        
        # 执行步骤
        if len(steps_to_execute) == 1:
            return await self._execute_single_step(steps_to_execute[0], state, config)
        else:
            return await self._execute_multiple_steps(steps_to_execute, state, config)
    
    def _get_max_concurrent_steps(self) -> int:
        """获取最大并发步骤数"""
        config = get_config()
        return config.get('reasoning', {}).get('execution', {}).get('concurrent', {}).get('max_concurrent_steps', 10)
    


    def _find_ready_steps(self, state: ReasoningState) -> List[Tuple[Step, int]]:
        """找到当前agent类型可以执行的步骤"""
        current_plan = state.get("current_plan")
        if not current_plan or not hasattr(current_plan, 'steps'):
            return []
        
        ready_steps = []
        
        for step in current_plan.steps:
            # 跳过已完成的步骤
            if step.step_status in [StepStatus.SUCCESS, StepStatus.FAILED, StepStatus.SKIP]:
                continue
            
            # 根据agent类型过滤步骤
            if not self._is_step_for_current_agent(step):
                continue
            
            # 检查依赖是否满足
            if self._are_dependencies_satisfied(step, current_plan.steps):
                ready_steps.append((step, step.step_order))
        
        # 按step_order排序
        ready_steps.sort(key=lambda x: x[1])
        return ready_steps
    
    def _is_step_for_current_agent(self, step: Step) -> bool:
        """检查步骤是否适合当前agent类型"""
        agent_type = self.agent.agent_type
        
        # researcher 处理 RESEARCH 类型的步骤
        if agent_type == "researcher":
            return step.step_type == StepType.RESEARCH
        
        # coder 处理 PROCESSING 类型的步骤
        elif agent_type == "coder":
            return step.step_type == StepType.PROCESSING
        
        # 其他agent类型默认不处理任何步骤
        return False
    
    
    def _are_dependencies_satisfied(self, step: Step, all_steps: List[Step]) -> bool:
        """检查步骤的依赖是否已满足"""
        if not step.prerequisite_steps:
            return True
        
        # 创建step_order到step的映射
        step_map = {s.step_order: s for s in all_steps}
        
        # 检查所有依赖步骤是否已成功完成
        for dep_order in step.prerequisite_steps:
            dep_step = step_map.get(dep_order)
            if not dep_step or dep_step.step_status != StepStatus.SUCCESS:
                return False
        
        return True
    
    async def _execute_single_step(self, step_info: Tuple[Step, int], 
                                 state: ReasoningState, config: RunnableConfig) -> Command:
        """执行单个步骤（保持原有逻辑）"""
        step, step_num = step_info
        
        try:
            # 标记为运行中
            step.step_status = StepStatus.RUNNING
            
            # 执行步骤（原有逻辑）
            tools = await self._prepare_tools(config)
            executor = await create_agent_with_tools(
                self.agent.agent_type, tools, config,
                step_title=step.title, step_description=step.description,
                request_id=state.get("request_id", "")
            )
            
            agent_input = self._prepare_input(state, step)
            result = await executor.ainvoke(
                input=agent_input, 
                config={"recursion_limit": get_recursion_limit(), "tags": [f"plan_step:{step_num}"]}
            )
            
            if not result or "messages" not in result or not result["messages"]:
                raise ValueError("Agent execution returned empty result")
            
            response_content = result["messages"][-1].content
            
            # 更新步骤状态
            step.step_status = StepStatus.SUCCESS
            step.execution_res = response_content
            step.observation = response_content
            
            return Command(
                update={
                    "messages": [HumanMessage(content=response_content, name=self.agent.agent_type)],
                    "observations": state.get("observations", []) + [response_content],
                },
                goto="research_team",
            )
            
        except Exception as e:
            # 更新为失败状态
            step.step_status = StepStatus.FAILED
            error_message = f"Step {step_num} failed: {str(e)}"
            step.execution_res = error_message
            step.observation = error_message
            
            return Command(
                update={
                    "messages": [HumanMessage(content=error_message, name=self.agent.agent_type)],
                    "observations": state.get("observations", []) + [error_message],
                },
                goto="research_team",
            )
    
    async def _execute_multiple_steps(self, steps_info: List[Tuple[Step, int]], 
                                    state: ReasoningState, config: RunnableConfig) -> Command:
        """并发执行多个步骤（简化版）"""
        
        # 标记所有步骤为运行中
        for step, _ in steps_info:
            step.step_status = StepStatus.RUNNING
        
        # 创建并发任务
        tasks = []
        for step, step_num in steps_info:
            task = asyncio.create_task(self._execute_one_step(step, step_num, state, config))
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        all_messages = []
        all_observations = state.get("observations", [])
        
        for i, result in enumerate(results):
            step, step_num = steps_info[i]
            
            if isinstance(result, Exception):
                # 处理异常
                error_msg = f"Step {step_num} failed: {str(result)}"
                step.step_status = StepStatus.FAILED
                step.execution_res = error_msg
                step.observation = error_msg
                all_messages.append(error_msg)
                all_observations.append(error_msg)
            else:
                # 处理成功结果
                step.step_status = StepStatus.SUCCESS
                step.execution_res = result
                step.observation = result
                all_messages.append(f"Step {step_num}: {result}")
                all_observations.append(result)
        
        combined_content = "\n\n".join(all_messages)
        
        return Command(
            update={
                "messages": [HumanMessage(content=combined_content, name=self.agent.agent_type)],
                "observations": all_observations,
            },
            goto="research_team",
        )
    
    async def _execute_one_step(self, step: Step, step_num: int, 
                              state: ReasoningState, config: RunnableConfig) -> str:
        """执行单个步骤并返回结果字符串"""
        tools = await self._prepare_tools(config)
        executor = await create_agent_with_tools(
            self.agent.agent_type, tools, config,
            step_title=step.title, step_description=step.description,
            request_id=state.get("request_id", "")
        )
        
        agent_input = self._prepare_input(state, step)
        result = await executor.ainvoke(
            input=agent_input, 
            config={"recursion_limit": get_recursion_limit(), "tags": [f"plan_step:{step_num}"]}
        )
        
        if not result or "messages" not in result or not result["messages"]:
            raise ValueError("Agent execution returned empty result")
        
        return result["messages"][-1].content
    
    
    async def _prepare_tools(self, config: RunnableConfig):
        """准备工具"""
        try:
            default_tools = self.agent.get_default_tools()
            
            # 对于coder代理，只使用默认工具（python_repl_tool），不加载MCP工具
            if self.agent.agent_type == "coder":
                return default_tools
            
            # 其他代理正常加载MCP工具
            mcp_tools = await setup_mcp_tools(self.agent.agent_type, config)
            return default_tools + mcp_tools
            
        except Exception as e:
            self.logger.error(f"Error preparing tools: {e}")
            import traceback
            self.logger.error(f"Tools preparation traceback:\n{traceback.format_exc()}")
            
            # 如果 MCP 工具失败，至少返回默认工具
            try:
                default_tools = self.agent.get_default_tools()
                return default_tools
            except Exception as fallback_error:
                self.logger.error(f"Even default tools failed: {fallback_error}")
                import traceback
                self.logger.error(f"Fallback traceback:\n{traceback.format_exc()}")
                return []
    
    def _prepare_input(self, state: ReasoningState, current_step):
        """准备执行输入"""
        context_builder = StepContextBuilder(state, current_step)
        input_data = context_builder.build_input(self.agent.agent_type)

        # 添加必要的状态字段
        input_data["request_id"] = state.get("request_id", "")
        input_data["sop_name"] = state.get("sop_id", "")

        return input_data


class StepContextBuilder:
    """步骤上下文构建器 - 负责构建清晰的执行上下文"""
    
    def __init__(self, state: ReasoningState, current_step):
        self.state = state
        self.current_step = current_step
        self.current_plan = state.get("current_plan")
    
    def build_input(self, agent_type: str) -> dict:
        """构建agent输入"""
        messages = []
        
        # 添加历史步骤上下文（按每个已完成步骤拆分为 user/ai 对）
        history_messages = self._build_history_messages()
        if history_messages:
            messages.extend(history_messages)
        
        # 添加当前任务
        current_task = self._build_current_task()
        messages.append(HumanMessage(content=current_task))
        
        
        return {
            "messages": messages
        }
    
    def _build_history_messages(self):
        """构建历史步骤消息对（User/AI）"""
        completed_steps = [
            step for step in self.current_plan.steps 
            if step.execution_res and step != self.current_step
        ]
        
        if not completed_steps:
            return []
        
        messages = []
        for step in completed_steps:
            # User message: title + description
            user_content = f"{step.title}\n\n{step.description}"
            messages.append(HumanMessage(content=user_content))
            
            # AI message: execution result
            ai_content = step.execution_res
            messages.append(AIMessage(content=ai_content))
        
        return messages
    
    def _build_current_task(self) -> str:
        """构建当前任务描述"""
        return f"""# 当前任务

## 任务
{self.current_step.title}

## 任务详情  
{self.current_step.description}
"""
    
