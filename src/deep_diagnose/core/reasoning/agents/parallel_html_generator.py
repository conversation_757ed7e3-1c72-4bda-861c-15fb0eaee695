"""
并发HTML报告生成器 - 基于LangChain原生RunnableParallel的高性能HTML生成系统

架构设计：
- 模块化设计：分离模板处理、链条创建和执行逻辑
- 高性能并发：LangChain原生RunnableParallel并行执行
- 智能错误恢复：多层fallback机制确保系统稳定性
- 可扩展接口：支持自定义模板处理器和合并策略

主要组件：
- HtmlChainFactory: 负责创建和配置HTML生成链条
- ParallelHtmlGeneratorAgent: 并发HTML生成的核心执行器
- TemplateProcessor: 可复用的模板渲染处理器
"""

import logging
from datetime import datetime
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List
from langchain_core.runnables import RunnableConfig, RunnableParallel, RunnablePassthrough
from langchain_core.runnables.base import Runnable
from langchain_core.output_parsers import StrOutputParser
from langgraph.types import Command

from .base import SubAgent
from deep_diagnose.core.reasoning.workflow.types import ReasoningState
from deep_diagnose.llms.agent_llm_config import AGENT_LLM_MAP
from deep_diagnose.llms.llm import get_llm_by_type
from deep_diagnose.prompts.template import env
from deep_diagnose.llms.agent_llm_config import SILENT_LLM_CALL_TAG

# 设置模块级日志记录器
logger = logging.getLogger(__name__)


class ErrorHandlingMixin:
    """错误处理混入类
    
    提供统一的错误处理和日志记录功能，可被其他类混入使用。
    """
    
    @property
    def logger(self):
        """获取日志记录器"""
        if not hasattr(self, '_logger'):
            self._logger = logger
        return self._logger
    
    def log_error(self, message: str, error: Exception, level: str = "error"):
        """统一的错误日志记录
        
        Args:
            message: 错误消息
            error: 异常对象
            level: 日志级别 (error, warning, info)
        """
        log_func = getattr(self.logger, level, self.logger.error)
        log_func(f"{message}: {error}", exc_info=(level == "error"))
    
    def log_info(self, message: str, **kwargs):
        """记录信息日志"""
        self.logger.info(message, **kwargs)
    
    def log_warning(self, message: str, **kwargs):
        """记录警告日志"""
        self.logger.warning(message, **kwargs)


class BaseTemplateProcessor(Runnable, ABC, ErrorHandlingMixin):
    """可复用的模板处理器基类
    
    提供标准化的模板渲染接口和错误处理机制
    """
    
    def __init__(self, template_name: str, section_name: str):
        self.template_name = template_name
        self.section_name = section_name
    
    def invoke(self, input_data: dict, config: Optional[RunnableConfig] = None) -> List[Dict[str, str]]:
        """渲染模板并返回格式化的消息列表"""
        try:
            return self._render_template(input_data)
        except Exception as e:
            self._log_error(f"模板 {self.template_name} 渲染失败", e)
            return self._get_fallback_messages()
    
    @abstractmethod
    def _render_template(self, input_data: dict) -> List[Dict[str, str]]:
        """子类实现具体的模板渲染逻辑"""
        pass
    
    def _get_fallback_messages(self) -> List[Dict[str, str]]:
        """获取fallback消息"""
        return [{"role": "system", "content": f"生成 {self.section_name} 的HTML内容"}]
    
    def _log_error(self, message: str, error: Exception):
        """记录错误日志"""
        self.log_error(message, error)


class Jinja2TemplateProcessor(BaseTemplateProcessor):
    """基于Jinja2的模板处理器实现"""
    
    def _render_template(self, input_data: dict) -> List[Dict[str, str]]:
        """使用Jinja2渲染模板"""
        # 构建模板变量
        template_variables = self._build_template_variables(input_data)
        
        # 渲染模板
        template = env.get_template(f"{self.template_name}.md")
        system_prompt = template.render(**template_variables)
        
        return [
            {"role": "system", "content": system_prompt}
        ] + input_data.get("messages", [])
    
    def _build_template_variables(self, input_data: dict) -> dict:
        """构建模板变量"""
        return {
            "CURRENT_TIME": datetime.now().strftime("%a %b %d %Y %H:%M:%S %z"),
            **input_data,
        }


class HtmlChainFactory(ErrorHandlingMixin):
    """HTML生成链条工厂类
    
    负责创建和配置各种HTML生成链条，提供统一的配置管理
    """
    
    DEFAULT_LLM_CONFIG = {
        "stream": False,
        "tags": [SILENT_LLM_CALL_TAG],
        "max_retries": 3,
        "retry_delay": 1.0
    }
    
    def __init__(self, llm_type: str = "reasoning"):
        self.llm_type = llm_type
        self._llm_instance = None
    
    @property
    def llm_instance(self):
        """延迟初始化LLM实例"""
        if self._llm_instance is None:
            self._llm_instance = self._create_configured_llm()
        return self._llm_instance
    
    def create_html_generation_chain(self, template_name: str, section_name: str) -> Runnable:
        """创建HTML生成链条
        
        Args:
            template_name: 模板名称
            section_name: 节名称
            
        Returns:
            完整的链条：TemplateProcessor -> LLM -> OutputParser
        """
        try:
            # 创建模板处理器
            template_processor = Jinja2TemplateProcessor(template_name, section_name)
            
            # 创建输出解析器
            output_parser = StrOutputParser()
            
            # 使用LCEL组合链条
            chain = template_processor | self.llm_instance | output_parser
            
            return chain
            
        except Exception as e:
            self._log_error(f"创建 {section_name} 链条失败", e)
            return self._create_fallback_chain(section_name, e)
    
    def _create_configured_llm(self):
        """创建配置好的LLM实例"""
        llm = get_llm_by_type(AGENT_LLM_MAP.get("html_generator", self.llm_type))
        return llm.with_config(self.DEFAULT_LLM_CONFIG)
    
    def _create_fallback_chain(self, section_name: str, error: Exception) -> Runnable:
        """创建fallback链条"""
        class FallbackChain(Runnable):
            def invoke(self, input_data: dict, config: Optional[RunnableConfig] = None) -> str:
                return f"<div class='error-section'>{section_name}生成失败: {error}</div>"
        
        return FallbackChain()
    
    def _log_error(self, message: str, error: Exception):
        """记录错误日志"""
        self.log_error(message, error)


class ParallelHtmlGeneratorAgent(SubAgent):
    """并发HTML生成代理
    
    使用LangChain原生RunnableParallel实现高性能并发HTML节生成。
    采用工厂模式管理链条创建，提供统一的配置和错误处理。
    """
    
    # HTML节配置定义
    HTML_SECTION_CONFIGS = [
        {"key": "problem_cover_html", "template": "section_problem_cover", "name": "problem_cover"},
        {"key": "problem_description_html", "template": "section_problem_description", "name": "problem_description"},
        {"key": "diagnosis_info_html", "template": "section_diagnosis_info", "name": "diagnosis_info"},
        {"key": "key_findings_html", "template": "section_key_findings", "name": "key_findings"},
        {"key": "evidence_chain_html", "template": "section_evidence_chain", "name": "evidence_chain"},
        {"key": "summary_conclusion_html", "template": "section_summary_conclusion", "name": "summary_conclusion"},
    ]
    
    def __init__(self):
        super().__init__("parallel_html_generator", "parallel_html_generator")
        self.chain_factory = HtmlChainFactory()
        self.parallel_chains = self._build_parallel_chains()
    
    def _build_parallel_chains(self) -> RunnableParallel:
        """构建并发执行链条
        
        使用配置驱动的方式创建所有HTML生成链条，提高可维护性。
        """
        chain_mappings = {}
        
        # 根据配置创建所有链条
        for config in self.HTML_SECTION_CONFIGS:
            chain_mappings[config["key"]] = self.chain_factory.create_html_generation_chain(
                config["template"], config["name"]
            )
        
        # 添加原始状态传递
        chain_mappings["original_state"] = RunnablePassthrough()
        
        return RunnableParallel(**chain_mappings)
    
    async def _do_execute(self, state: ReasoningState, config: Optional[RunnableConfig] = None) -> Command:
        """[核心执行方法] 执行并发HTML生成
        
        Args:
            state: 推理状态
            config: 运行时配置
            
        Returns:
            包含生成的HTML节的命令
        """
        try:
            # 构建输入数据
            generation_context = self._build_generation_context(state)
            
            # 执行并发生成
            parallel_results = await self.parallel_chains.ainvoke(generation_context)
            
            # 提取和清理结果
            html_sections = self._extract_html_sections(parallel_results)
            
            self._log_generation_success(html_sections)
            return Command(update=html_sections)
            
        except Exception as e:
            self.logger.error(f"LangChain原生RunnableParallel并发HTML生成失败: {e}", exc_info=True)
            return Command(update=self._get_fallback_html_sections())
    
    def _build_generation_context(self, state: ReasoningState) -> Dict[str, Any]:
        """构建生成上下文
        
        提取并整理所有HTML生成器所需的共享信息。
        """
        messages = state.get("messages", [])
        user_query = ""
        if messages and hasattr(messages[0], 'content'):
            user_query = messages[0].content
        elif messages:
            user_query = str(messages[0])
        
        return {
            "user_query": user_query,
            "result":state.get("final_report", ""),
            "final_report": state.get("final_report", ""),
            "observations": state.get("observations", []),
            "current_plan": state.get("current_plan", ""),
            "messages": messages,
        }
    
    def _extract_html_sections(self, parallel_results: Dict[str, Any]) -> Dict[str, str]:
        """从并发结果中提取HTML节
        
        安全地处理并发执行结果，确保稳定性。
        """
        html_sections = {}
        
        for config in self.HTML_SECTION_CONFIGS:
            section_key = config["key"]
            section_name = config["name"]
            default_content = f"<div class='error-section'>{section_name}生成失败</div>"
            
            html_sections[section_key] = self._safe_extract_result(
                parallel_results, section_key, default_content
            )
        
        return html_sections
    
    def _safe_extract_result(self, results: Dict[str, Any], key: str, default_content: str) -> str:
        """安全地提取单个结果
        
        处理可能的异常情况，确保返回有效内容。
        """
        try:
            content = results.get(key, default_content)
            
            if isinstance(content, str) and content.strip():
                return content.strip()
            else:
                self.logger.warning(f"获取 {key} 结果为空，使用默认内容")
                return default_content
                
        except Exception as e:
            self.logger.warning(f"获取 {key} 结果失败: {e}")
            return default_content
    
    def _get_fallback_html_sections(self) -> Dict[str, str]:
        """获取fallback HTML节
        
        在发生错误时提供的默认HTML内容。
        """
        fallback_sections = {}
        
        for config in self.HTML_SECTION_CONFIGS:
            section_key = config["key"]
            section_name = config["name"]
            fallback_sections[section_key] = f"<div class='error-section'>{section_name}生成失败</div>"
        
        return fallback_sections
    
    def _log_generation_success(self, html_sections: Dict[str, str]):
        """记录生成成功日志"""
        section_lengths = {key: len(content) for key, content in html_sections.items()}
        self.logger.info(
            f"HTML节并发生成成功，节长度: {section_lengths}"
        )


# 单例实例
_parallel_html_generator = ParallelHtmlGeneratorAgent()


# LangGraph节点函数
async def parallel_html_generator_node(state: ReasoningState, config: Optional[RunnableConfig] = None) -> Dict[str, Any]:
    """LangGraph 节点：执行LangChain原生RunnableParallel并发HTML生成"""
    return await _parallel_html_generator.execute(state, config)