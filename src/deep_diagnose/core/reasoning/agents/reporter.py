"""
报告Agent - 负责生成最终诊断报告
"""

from typing import Optional
from langchain_core.messages import HumanMessage
from langchain_core.runnables import RunnableConfig

from .base import SubAgent
from deep_diagnose.core.reasoning.workflow.types import ReasoningState
from deep_diagnose.llms.agent_llm_config import AGENT_LLM_MAP
from deep_diagnose.llms.llm import get_llm_by_type
from deep_diagnose.prompts.template import apply_prompt_template


from deep_diagnose.core.reasoning.reporting.reporter_template_manager import get_reporter_template_manager

class ReporterAgent(SubAgent):
    """报告Agent"""
    
    def __init__(self):
        super().__init__("reporter", "reporter")
    
    async def _do_execute(self, state: ReasoningState, config: Optional[RunnableConfig] = None):
        current_plan = state.get("current_plan")
        
        # 选择 Reporter 模板并注入到状态
        try:
            manager = get_reporter_template_manager()
            tpl_content, tpl_name, tpl_reason = await manager.select_and_load(state)
            state["reporter_template_content"] = tpl_content
            state["reporter_template_name"] = tpl_name
            state["reporter_template_reason"] = tpl_reason
        except Exception as e:
            self.logger.warning(f"Reporter 模板选择失败: {e}")
            state["reporter_template_content"] = ""
            state["reporter_template_name"] = ""
            state["reporter_template_reason"] = str(e)

        # 准备输入
        plan_title = getattr(current_plan, "title", None) or (current_plan.get("title") if isinstance(current_plan, dict) else "")
        plan_thought = getattr(current_plan, "thought", None) or (current_plan.get("thought") if isinstance(current_plan, dict) else "")

        input_data = {
            "messages": [
                HumanMessage(f"# 诊断需求 \n\n## 任务\n\n{plan_title}\n\n## 描述\n\n{plan_thought}")
            ],
            
            "reporter_template_content": state.get("reporter_template_content", ""),
            "reporter_template_name": state.get("reporter_template_name", ""),
            "reporter_template_reason": state.get("reporter_template_reason", ""),
            "sop_name": state.get("sop_name", ""),
        }
        
        # 生成消息（注入 reporter 模板内容）
        messages = apply_prompt_template("agent_reporter", input_data)
        
        # 添加格式提醒
        messages.append(
            HumanMessage(
                content="IMPORTANT: 按照prompt格式构建报告，包含：关键要点、概述、详细分析、引用。优先使用Markdown表格展示数据。",
                name="system",
            )
        )
        
        # 添加观察结果
        for observation in state.get("observations", []):
            messages.append(
                HumanMessage(
                    content=f"研究观察结果:\n\n{observation}",
                    name="observation",
                )
            )
        
        # 生成报告
        response = await get_llm_by_type(AGENT_LLM_MAP["reporter"]).ainvoke(messages)
        self.logger.info("Report generated successfully")
        
        return {"final_report": response.content}


# 创建实例和节点函数
_reporter = ReporterAgent()

async def reporter_node(state: ReasoningState):
    return await _reporter.execute(state)