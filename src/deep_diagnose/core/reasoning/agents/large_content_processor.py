"""
Large Content Processor - 大内容处理器

提供通用的大型工具输出内容处理能力，支持分片、并发分析和结果合并。
"""

import asyncio
import re
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Dict, Any, Optional, Callable
from langchain_core.messages import BaseMessage, HumanMessage
from langchain_core.runnables import RunnableConfig

from deep_diagnose.llms.agent_llm_config import AGENT_LLM_MAP, SILENT_LLM_CALL_TAG
from deep_diagnose.llms.llm import get_llm_by_type


@dataclass
class ContentChunk:
    """内容分片数据模型"""
    content: str
    chunk_id: int
    total_chunks: int
    metadata: Dict[str, Any] = None


class ContentProcessor(ABC):
    """内容处理器抽象基类"""
    
    @abstractmethod
    def should_process(self, tool_name: str, content: str) -> bool:
        """判断是否需要处理此内容"""
        pass
    
    @abstractmethod
    def create_analysis_prompt(self, chunk: ContentChunk) -> str:
        """创建分析提示词"""
        pass
    
    @abstractmethod
    def merge_results(self, results: List[str]) -> str:
        """合并分析结果"""
        pass
    
    def get_llm_type(self) -> str:
        """获取使用的LLM类型"""
        return "basic"
    
    def get_chunk_size(self) -> int:
        """获取分片大小"""
        return 1000


class LogContentProcessor(ContentProcessor):
    """日志内容处理器 - 专门处理NC宿主机日志"""
    
    def should_process(self, tool_name: str, content: str) -> bool:
        """判断是否需要处理 - NC日志且内容较大"""
        return tool_name == "getNcDownLog" and len(content) > 1000
    
    def create_analysis_prompt(self, chunk: ContentChunk) -> str:
        """创建NC日志分析提示词"""
        # 对日志内容进行去重处理
        deduplicated_content = self._deduplicate_log_content(chunk.content)
        
        return f"""你是一个专业的日志分析师，专门负责分析NC宿主机宕机相关的日志。

你的任务是从以下日志分片中提取与宿主机宕机相关的关键信息，特别关注性能数据和时间线分析：

**核心分析重点：**
1. **宕机前的关键日志（性能数据）：**
   - CPU使用率、负载异常（load average, %CPU）
   - 内存使用情况（内存泄漏、OOM killer、可用内存不足）
   - 磁盘I/O异常（磁盘满、I/O错误、磁盘延迟）
   - 网络异常（网络超时、连接失败、丢包）
   - 进程状态异常（进程卡死、僵尸进程、资源耗尽）
   - 系统服务状态变化

2. **宕机前和宕机相关的调用栈：**
   - 宕机前最后的函数调用链
   - Call Trace、Stack trace、Backtrace等完整调用栈
   - 函数调用序列和地址信息
   - CPU寄存器状态（RIP、RSP、RBP等）
   - 内核模块和符号信息
   - 异常处理路径和错误传播链

**次要分析要求：**
3. 找出内核panic、oops、段错误等关键信息
4. 找出硬件故障、网络异常等信息
5. 保留完整时间戳和错误代码

**输出要求：**
- 只输出与宕机相关的日志行（最多500行，优先保留性能数据和调用栈）
- **严格按时间顺序排列，构建宕机前的时间线**
- 保持原始日志格式
- **对于性能数据和调用栈信息，必须完整保留，不允许截断或压缩**
- 标注关键时间点和性能拐点
- 相似重复的普通日志只需要保留1-2行代表性样本
- 如果没有相关内容，返回——本分片无宕机相关日志——

**当前分片信息：**
分片 {chunk.chunk_id + 1}/{chunk.total_chunks}

**日志内容（已去重处理）：**
{deduplicated_content}
"""
    
    def merge_results(self, results: List[str]) -> str:
        """合并NC日志分析结果"""
        if not results:
            return "无分析结果"
        
        # 过滤空结果和无关内容
        valid_results = []
        for result in results:
            if result and result.strip():
                if "——本分片无宕机相关日志——" not in result:
                    valid_results.append(result.strip())
        
        if not valid_results:
            return "未找到与宕机相关的日志信息"
        
        # 合并所有有效结果
        merged_content = "\n\n=== 分片分隔线 ===\n\n".join(valid_results)
        
        # 统计信息
        total_chunks = len(results)
        valid_chunks = len(valid_results)
        
        header = f"""【NC宿主机宕机相关日志分析结果】
处理统计: 共{total_chunks}个分片，{valid_chunks}个包含相关信息

"""
        return header + merged_content
    
    def get_llm_type(self) -> str:
        return "extract_log"
    
    def _deduplicate_log_content(self, content: str) -> str:
        """去重日志内容"""
        if not content:
            return content
        
        lines = content.split('\n')
        deduplicated_lines = []
        seen_patterns = {}  # 改为字典，保存模式和对应的最早时间行
        
        for line in lines:
            if not line.strip():
                continue
            
            pattern = self._extract_log_pattern(line)
            
            if pattern not in seen_patterns:
                # 第一次出现该模式，直接保存
                seen_patterns[pattern] = line
                deduplicated_lines.append(line)
            else:
                # 模式已存在，检查是否需要更新为更早的时间
                existing_line = seen_patterns[pattern]
                existing_time = self._extract_timestamp(existing_line)
                current_time = self._extract_timestamp(line)
                
                # 如果当前行的时间更早，则替换
                if current_time and existing_time and current_time < existing_time:
                    # 在结果列表中替换为更早的时间
                    for i, saved_line in enumerate(deduplicated_lines):
                        if saved_line == existing_line:
                            deduplicated_lines[i] = line
                            seen_patterns[pattern] = line
                            break
        
        return '\n'.join(deduplicated_lines)
    
    def _extract_log_pattern(self, log_line: str) -> str:
        """提取日志行的模式（移除时间戳和变量部分，保留结构）"""
        # 移除时间戳（多种格式）
        pattern = re.sub(r'\d{4}-\d{2}-\d{2}[\s\t]\d{2}:\d{2}:\d{2}[.\d]*', '[TIMESTAMP]', log_line)
        pattern = re.sub(r'\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2}', '[TIMESTAMP]', pattern)
        
        # 移除变量部分
        pattern = re.sub(r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b', '[IP]', pattern)
        pattern = re.sub(r'\[\d+\]', '[PID]', pattern)
        pattern = re.sub(r'pid:\s*\d+', 'pid:[PID]', pattern)
        pattern = re.sub(r'0x[0-9a-fA-F]+', '[ADDR]', pattern)
        pattern = re.sub(r'\b\d{4,}\b', '[NUM]', pattern)
        
        return re.sub(r'\s+', ' ', pattern).strip()
    
    def _extract_timestamp(self, log_line: str) -> Optional[str]:
        """提取日志行的时间戳"""
        # 匹配常见的时间戳格式
        # 格式1: 2024-01-01 12:00:00.123
        match = re.search(r'\d{4}-\d{2}-\d{2}[\s\t]\d{2}:\d{2}:\d{2}[.\d]*', log_line)
        if match:
            return match.group()
        
        # 格式2: Jan 01 12:00:00
        match = re.search(r'\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2}', log_line)
        if match:
            return match.group()
        
        return None
    


class ContentChunker:
    """内容分片器 - 负责将大内容分割为可处理的小块"""
    
    @staticmethod
    def chunk_content(content: str, max_lines_per_chunk: int = 1000) -> List[ContentChunk]:
        """
        将内容按行数分割为小块
        
        Args:
            content: 原始内容
            max_lines_per_chunk: 每个分片的最大行数
        
        Returns:
            ContentChunk 列表
        """
        if not content or not content.strip():
            return []
        
        lines = content.split('\n')
        total_lines = len(lines)
        
        if total_lines <= max_lines_per_chunk:
            return [ContentChunk(content=content, chunk_id=0, total_chunks=1)]
        
        chunks = []
        chunk_id = 0
        
        for i in range(0, total_lines, max_lines_per_chunk):
            chunk_lines = lines[i:i + max_lines_per_chunk]
            chunk_content = '\n'.join(chunk_lines)
            chunks.append(ContentChunk(
                content=chunk_content,
                chunk_id=chunk_id,
                total_chunks=0  # 会在后面更新
            ))
            chunk_id += 1
        
        # 更新总分片数
        total_chunks = len(chunks)
        for chunk in chunks:
            chunk.total_chunks = total_chunks
        
        return chunks


class ConcurrentAnalyzer:
    """并发分析器 - 负责并发处理多个内容分片"""
    
    @staticmethod
    async def analyze_chunks(chunks: List[ContentChunk], processor: ContentProcessor) -> List[str]:
        """
        并发分析多个内容分片
        
        Args:
            chunks: 内容分片列表
            processor: 内容处理器
        
        Returns:
            分析结果列表
        """
        if not chunks:
            return []
        
        # 创建并发任务
        tasks = []
        for chunk in chunks:
            task = ConcurrentAnalyzer._analyze_single_chunk(chunk, processor)
            tasks.append(task)
        
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果和异常
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    processed_results.append(f"分片 {i} 分析失败: {str(result)}")
                else:
                    processed_results.append(result)
            
            return processed_results
            
        except Exception as e:
            return [f"并发分析失败: {str(e)}"]
    
    @staticmethod
    async def _analyze_single_chunk(chunk: ContentChunk, processor: ContentProcessor) -> str:
        """分析单个内容分片"""
        try:
            # 获取LLM
            llm_type = AGENT_LLM_MAP.get(processor.get_llm_type(), "basic")
            llm = get_llm_by_type(llm_type)
            
            # 构建分析消息
            prompt = processor.create_analysis_prompt(chunk)
            messages = [HumanMessage(content=prompt)]
            
            # 配置静默调用
            config = RunnableConfig(tags=[SILENT_LLM_CALL_TAG])
            
            # 执行分析
            response = await llm.ainvoke(messages, config)
            
            # 提取响应内容
            if hasattr(response, 'content'):
                return response.content.strip()
            else:
                return str(response).strip()
            
        except Exception as e:
            return f"分片 {chunk.chunk_id} 分析错误: {str(e)}"


class LargeContentManager:
    """大内容管理器 - 统一的大内容处理入口"""
    
    def __init__(self):
        self.processors: List[ContentProcessor] = [
            LogContentProcessor(),
            # 可以在这里添加更多专用处理器
        ]
    
    def register_processor(self, processor: ContentProcessor):
        """注册新的内容处理器"""
        self.processors.append(processor)
    
    async def process_large_content(self, tool_name: str, content: str) -> Optional[str]:
        """
        处理大型内容的通用入口
        
        Args:
            tool_name: 工具名称
            content: 原始内容
        
        Returns:
            处理后的内容，如果不需要处理则返回None
        """
        # 查找适合的处理器
        processor = self._find_processor(tool_name, content)
        if not processor:
            return None
        
        try:
            # 1. 分片处理
            chunks = ContentChunker.chunk_content(content, processor.get_chunk_size())
            
            if not chunks:
                return "内容为空"
            
            # 如果只有一个分片，直接返回原内容
            if len(chunks) == 1:
                return content
            
            # 2. 并发分析
            analysis_results = await ConcurrentAnalyzer.analyze_chunks(chunks, processor)
            
            # 3. 合并结果
            merged_result = processor.merge_results(analysis_results)
            
            return merged_result
            
        except Exception as e:
            return f"大内容处理失败: {str(e)}\n\n原始输出:\n{content}"
    
    def _find_processor(self, tool_name: str, content: str) -> Optional[ContentProcessor]:
        """查找适合的内容处理器"""
        for processor in self.processors:
            if processor.should_process(tool_name, content):
                return processor
        return None


# 全局实例
large_content_manager = LargeContentManager()