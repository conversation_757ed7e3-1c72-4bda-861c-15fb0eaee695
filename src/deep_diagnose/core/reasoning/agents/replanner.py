"""
重新规划器Agent - 基于步骤执行结果动态调整计划
"""

import json
from typing import Literal, Optional
from langchain_core.messages import AIMessage
from langchain_core.runnables import RunnableConfig
from langgraph.types import Command

from .base import SubAgent
from deep_diagnose.core.reasoning.workflow.types import ReasoningState
from deep_diagnose.llms.agent_llm_config import AGENT_LLM_MAP
from deep_diagnose.common.config.core.configuration import Configuration
from deep_diagnose.llms.llm import get_llm_by_type
from deep_diagnose.prompts.template import apply_prompt_template
from deep_diagnose.prompts.planner_model import Plan, StepStatus
from deep_diagnose.common.utils.json_utils import repair_json_output
from deep_diagnose.tools.mcp.mcp_tool_manager import MCPToolManager
from deep_diagnose.core.reasoning.planning import create_sop_service


class ReplannerAgent(SubAgent):
    """重新规划器Agent - 基于依赖步骤的执行结果重新规划后续步骤"""
    
    def __init__(self, config_obj: Configuration):
        super().__init__("replanner", "replanner")
        # Initialize SOPService
        self.sop_service = create_sop_service()
    
    async def _do_execute(self, state: ReasoningState, config: Optional[RunnableConfig] = None) -> Command:
        """执行重新规划逻辑"""
        configurable = Configuration.from_runnable_config(config)
        
        current_plan = state.get("current_plan")
        if not current_plan or not hasattr(current_plan, 'steps'):
            return Command(goto="planner")
        
        # 找到需要重新规划的步骤
        steps_to_replan = self._find_steps_to_replan(current_plan)
        if not steps_to_replan:
            return Command(goto="research_team")
        
        # 格式化旧planning内容
        old_planning = self._format_old_planning(current_plan)
        
        # 生成新planning
        new_planning = await self._generate_new_planning(old_planning, state, configurable)
        
        # 更新计划
        self._update_plan(current_plan, new_planning, steps_to_replan)
        
        return Command(
            update={"current_plan": current_plan},
            goto="research_team"
        )
    
    def _find_steps_to_replan(self, current_plan: Plan) -> list:
        """找到需要重新规划的步骤"""
        steps_to_replan = []
        
        # 找到最近完成的步骤
        recently_completed_orders = set()
        for step in current_plan.steps:
            if (hasattr(step, 'step_status') and step.step_status == StepStatus.SUCCESS and
                hasattr(step, 'execution_res') and step.execution_res):
                recently_completed_orders.add(getattr(step, 'step_order', 0))
        
        # 找到依赖最近完成步骤的未执行步骤
        for step in current_plan.steps:
            if hasattr(step, 'step_status') and step.step_status != StepStatus.NOT_START:
                continue
            
            prerequisite_steps = getattr(step, 'prerequisite_steps', [])
            if prerequisite_steps and any(dep in recently_completed_orders for dep in prerequisite_steps):
                steps_to_replan.append(step)
        
        return steps_to_replan
    
    async def _prepare_sop_state(self, state: ReasoningState):
        """准备SOP状态信息"""
        # 如果state中已经有SOP信息，直接复用
        if state.get("operation_sop_content") and  state.get("sop_id"):
            return
        if  state.get("sop_id"):
            state["operation_sop_content"] = self.sop_service.store.get_sop_by_id(state.get("sop_id")).content
            return

    
    def _extract_user_query(self, state: ReasoningState) -> str:
        """提取用户查询"""
        if not state.get("messages"):
            return ""
        for msg in reversed(state["messages"]):
            if hasattr(msg, 'content') and msg.content:
                return msg.content
        return ""
    
    def _format_old_planning(self, current_plan: Plan) -> str:
        """格式化旧planning内容"""
        return current_plan.model_dump_json(indent=2, exclude_none=True)
    
    async def _generate_new_planning(self, old_planning: str, state: ReasoningState, configurable: Configuration) -> str:
        """生成新planning"""

        # 准备SOP内容 - 复用现有的SOP信息或重新获取
        await self._prepare_sop_state(state)
        
        template_state = {
            "original_plan_formatted": old_planning,
            "CURRENT_TIME": state.get("CURRENT_TIME", ""),
            "mcp_servers_description": state.get("mcp_servers_description", ""),
            "operation_sop_content": state.get("operation_sop_content", ""),
            "sop_id": state.get("sop_id", ""),
            "messages": []  # 添加空的messages字段
        }
        
        messages = apply_prompt_template("agent_replanner", template_state, configurable)
        llm = get_llm_by_type(AGENT_LLM_MAP.get("replanner", "basic"))
        
        response = llm.astream(messages)
        full_response = ""
        async for chunk in response:
            full_response += chunk.content
        return full_response
    
    def _update_plan(self, current_plan: Plan, new_planning: str, steps_to_replan: list):
        """更新计划"""
        try:
            new_data = json.loads(repair_json_output(new_planning))
            if "steps" in new_data:
                # 简单更新：只更新title和description
                for new_step in new_data["steps"]:
                    step_order = new_step.get("step_order")
                    for step in steps_to_replan:
                        if getattr(step, 'step_order', 0) == step_order:
                            if "title" in new_step:
                                step.title = new_step["title"]
                            if "description" in new_step:
                                step.description = new_step["description"]
                            break
        except:
            pass  # 如果解析失败，保持原计划


async def replanner_node(state: ReasoningState, config: RunnableConfig) -> Command[Literal["research_team", "planner"]]:
    """重新规划器节点函数"""
    configurable = Configuration.from_runnable_config(config)
    _replanner = ReplannerAgent(configurable)
    return await _replanner.execute(state, config)