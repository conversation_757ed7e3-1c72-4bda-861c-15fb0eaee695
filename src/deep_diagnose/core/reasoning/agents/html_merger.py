"""
HTML报告合并器 - 重构版本

重构原则：
1. 将非核心功能拆解到独立的服务层
2. 代理类专注于工作流编排和状态管理  
3. 依赖注入提高可测试性和可扩展性
4. 保持核心业务逻辑的清晰性
"""

import logging
from typing import Optional, Dict, Any
from langchain_core.runnables import RunnableConfig
from langgraph.types import Command
from langgraph.graph import END

from .base import SubAgent
from deep_diagnose.core.reasoning.workflow.types import ReasoningState
from deep_diagnose.core.reasoning.services import (
    HtmlProcessingService,
    StorageService,
    ReportGenerationService
)

logger = logging.getLogger(__name__)


class HtmlReportMergerAgent(SubAgent):
    """HTML报告合并代理 - 重构版本，使用服务层架构"""
    
    def __init__(self, 
                 html_processing_service: Optional[HtmlProcessingService] = None,
                 storage_service: Optional[StorageService] = None,
                 report_generation_service: Optional[ReportGenerationService] = None):
        """
        初始化HTML报告合并代理
        
        Args:
            html_processing_service: HTML处理服务
            storage_service: 存储服务
            report_generation_service: 报告生成服务
        """
        super().__init__("html_report_merger", "html_report_merger")
        
        # 依赖注入，如果没有提供则使用默认实现
        self.html_processing_service = html_processing_service or HtmlProcessingService()
        self.storage_service = storage_service or StorageService()
        self.report_generation_service = report_generation_service or ReportGenerationService()
    
    async def _do_execute(self, state: ReasoningState, config: Optional[RunnableConfig] = None) -> Command:
        """执行HTML报告合并 - 使用服务层架构"""
        try:
            # 1. 提取HTML片段和用户查询
            sections = self.report_generation_service.extract_sections_from_state(state)
            user_query = self.report_generation_service.extract_user_query_from_state(state)
            
            # 2. 生成完整的HTML报告
            complete_html = await self.report_generation_service.generate_complete_report(
                sections, state, user_query
            )
            
            # 3. 验证HTML质量，失败则使用回退方案
            if not self.html_processing_service.validate_html(complete_html):
                logger.warning("HTML质量验证失败，使用回退报告")
                final_report = state.get("final_report", "")
                title = self.html_processing_service.extract_title_from_content(
                    final_report or user_query
                )
                complete_html = self.html_processing_service.generate_fallback_report(sections, title)
            
            # 4. 合并性能数据
            request_id = state.get("request_id", "unknown")
            enhanced_html = await self.report_generation_service.merge_performance_data(
                complete_html, request_id
            )
            
            # 5. 上传到存储并获取API URL
            api_url = await self.storage_service.upload_and_store_report(enhanced_html, request_id)
            
            logger.info(f"HTML报告合并完成 - 内容长度: {len(enhanced_html)}")
            
            # 6. 更新状态
            update_data = {
                "merged_html_report": enhanced_html
            }
            
            # 如果有API URL，添加到state中供后续使用
            if api_url:
                update_data["report_urls"] = [api_url]
                logger.info(f"✅ 报告URL已添加到state: {api_url}")
            
            return Command(update=update_data, goto=END)
            
        except Exception as e:
            logger.error(f"HTML报告合并失败: {e}", exc_info=True)
            return Command(update={}, goto=END)
    


# 单例和节点函数 - 使用依赖注入
_html_report_merger = None

def get_html_report_merger():
    """获取HTML报告合并器实例，支持依赖注入"""
    global _html_report_merger
    if _html_report_merger is None:
        _html_report_merger = HtmlReportMergerAgent()
    return _html_report_merger

async def html_report_merger_node(state: ReasoningState, config: Optional[RunnableConfig] = None) -> Dict[str, Any]:
    """LangGraph 节点：执行HTML报告合并"""
    merger = get_html_report_merger()
    return await merger.execute(state, config)
