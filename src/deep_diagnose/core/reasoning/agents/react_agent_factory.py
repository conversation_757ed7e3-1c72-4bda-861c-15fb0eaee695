"""
React Agent Factory - 简化重构版本
保持可读性的同时控制代码量
"""

import json
import asyncio
from dataclasses import dataclass
from enum import Enum
from typing import List, Any, Optional, Dict, Tuple
from langchain_core.messages import ToolMessage, BaseMessage, AIMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.prebuilt import create_react_agent

from deep_diagnose.common.config.core.configuration import Configuration
from deep_diagnose.common.config.constants.workflow import DEFAULT_MAX_RECURSIVE_NUM
from deep_diagnose.common.config import get_config
from deep_diagnose.llms.agent_llm_config import AGENT_LLM_MAP, SILENT_LLM_CALL_TAG
from deep_diagnose.llms.llm import get_llm_by_type
from deep_diagnose.prompts.template import apply_prompt_template
from deep_diagnose.tools.mcp.mcp_tool_manager import MCPToolManager
from deep_diagnose.core.reasoning.workflow.types import ReasoningState
from deep_diagnose.core.reasoning.planning import create_sop_service


# ==================== 数据模型 ====================

@dataclass
class ToolAnalysis:
    """工具执行结果分析 - 保持原有接口"""
    is_tool: bool
    success: bool
    empty: bool
    text: str






class ExecutionPhase(str, Enum):
    """执行阶段"""
    SELECT = "select"
    FINAL = "final"


# ==================== 核心分析器 ====================

class ContentParser:
    """内容解析器 - 统一处理内容解析逻辑"""
    
    @staticmethod
    def is_empty(content: Any) -> bool:
        """判断内容是否为空"""
        if content is None:
            return True
        if isinstance(content, str):
            return len(content.strip()) == 0
        if isinstance(content, (list, tuple, set, dict)):
            return len(content) == 0
        if isinstance(content, (int, float, bool)):
            return False
        
        try:
            str_val = str(content).strip()
            return len(str_val) == 0 or str_val.lower() in ('none', 'null')
        except Exception:
            return False
    
    @staticmethod
    def parse_json_safely(raw_content: str) -> Tuple[str, bool]:
        """安全解析JSON内容"""
        if not raw_content:
            return "", True
        
        try:
            parsed = json.loads(raw_content)
            is_empty = ContentParser.is_empty(parsed)
            formatted = json.dumps(parsed, ensure_ascii=False, indent=2) if isinstance(parsed, (dict, list)) else str(parsed)
            return formatted, is_empty
        except json.JSONDecodeError:
            return raw_content, ContentParser.is_empty(raw_content)


class MessageAnalyzer:
    """消息分析器 - 整合原有分析逻辑"""
    
    @staticmethod
    async def analyze_tool_message(messages: List[BaseMessage]) -> ToolAnalysis:
        """分析工具消息 - 兼容原有接口"""
        if not messages:
            return ToolAnalysis(False, False, True, "")
        
        # 提取最近的工具消息组
        tool_messages = MessageAnalyzer._extract_recent_tool_group(messages)
        if not tool_messages:
            return ToolAnalysis(False, False, True, "")
        
        # 分析成功状态和格式化输出
        success_count = 0
        all_empty = True
        formatted_parts = []
        
        # 查找对应的AI消息
        ai_message = MessageAnalyzer._find_ai_message(messages, len(tool_messages))
        
        for tool_msg in tool_messages:
            single_analysis = MessageAnalyzer._analyze_single_tool(tool_msg)
            if single_analysis.success:
                success_count += 1
            if not single_analysis.empty:
                all_empty = False
            
            # 格式化单个工具结果
            if ai_message:
                formatted_part = await MessageAnalyzer._format_with_ai_context(tool_msg, ai_message)
            else:
                formatted_part = f"Tool: {getattr(tool_msg, 'name', 'unknown')}\nResult: {single_analysis.text}"
            formatted_parts.append(formatted_part)
        
        combined_text = '\n\n'.join(formatted_parts)
        overall_success = success_count > 0
        
        return ToolAnalysis(True, overall_success, all_empty, combined_text)
    
    @staticmethod
    def count_consecutive_failures(messages: List[BaseMessage]) -> int:
        """统计同一工具的连续失败次数"""
        if not messages:
            return 0
        
        # 找到最后一个工具的名称
        target_tool = None
        for msg in reversed(messages):
            if isinstance(msg, ToolMessage):
                target_tool = getattr(msg, 'name', 'unknown')
                break
        
        if not target_tool:
            return 0
        
        # 统计该工具的连续失败次数
        count = 0
        for msg in reversed(messages):
            if isinstance(msg, ToolMessage) and getattr(msg, 'name', 'unknown') == target_tool:
                analysis = MessageAnalyzer._analyze_single_tool(msg)
                if analysis.success:
                    break
                count += 1
        
        return count
    
    @staticmethod
    def get_failure_counts(messages: List[BaseMessage]) -> Tuple[int, int]:
        """获取失败计数 - 返回 (同工具失败次数, 任意工具失败次数)"""
        if not messages:
            return 0, 0
        
        same_tool_failures = MessageAnalyzer.count_consecutive_failures(messages)
        
        # 统计任意工具的连续失败
        any_tool_failures = 0
        for msg in reversed(messages):
            if isinstance(msg, ToolMessage):
                analysis = MessageAnalyzer._analyze_single_tool(msg)
                if analysis.success:
                    break
                any_tool_failures += 1
        
        return same_tool_failures, any_tool_failures
    
    @staticmethod
    def _extract_recent_tool_group(messages: List[BaseMessage]) -> List[ToolMessage]:
        """提取最近的连续工具消息组"""
        tool_messages = []
        for msg in reversed(messages):
            if isinstance(msg, ToolMessage):
                tool_messages.insert(0, msg)
            else:
                break
        return tool_messages
    
    @staticmethod
    def _find_ai_message(messages: List[BaseMessage], tool_count: int) -> Optional[AIMessage]:
        """查找对应的AI消息"""
        start_index = len(messages) - tool_count - 1
        for i in range(start_index, -1, -1):
            msg = messages[i]
            if hasattr(msg, 'tool_calls') and msg.tool_calls:
                return msg
        return None
    
    @staticmethod
    def _analyze_single_tool(msg: ToolMessage) -> ToolAnalysis:
        """分析单个工具消息"""
        # 判断成功状态
        success = True
        if hasattr(msg, 'status') and msg.status is not None:
            success = str(msg.status).lower() == "success"
        else:
            # 简单的启发式判断
            content_str = str(msg.content).lower() if msg.content else ""
            error_indicators = ['error', 'failed', 'exception', 'traceback']
            success = not any(indicator in content_str for indicator in error_indicators)
        
        # 解析内容
        raw_content = str(msg.content) if msg.content is not None else ""
        formatted_content, is_empty = ContentParser.parse_json_safely(raw_content)
        
        return ToolAnalysis(True, success, is_empty, formatted_content)
    
    @staticmethod
    async def _format_with_ai_context(tool_msg: ToolMessage, ai_msg: AIMessage) -> str:
        """使用AI上下文格式化工具结果"""
        # 查找对应的tool_call
        tool_call = None
        if hasattr(ai_msg, 'tool_calls') and ai_msg.tool_calls:
            for call in ai_msg.tool_calls:
                if call.get('id') == getattr(tool_msg, 'tool_call_id', ''):
                    tool_call = call
                    break
        
        # 获取过滤配置
        try:
            filtered_tools = get_config().mcp_data_filter or []
        except (AttributeError, KeyError):
            filtered_tools = []
        
        if tool_call:
            tool_name = tool_call.get("name", "unknown")
            input_data = {
                "name": tool_name,
                "args": tool_call.get("args", {}),
                "id": tool_call.get("id", "unknown")
            }
            input_str = json.dumps(input_data, ensure_ascii=False, indent=2)
            
            # 检查工具名是否在过滤列表中，决定输出内容
            if tool_name in filtered_tools:
                output_content = "[数据已经输出，数据过大，不做显示]"
            else:
                analysis = MessageAnalyzer._analyze_single_tool(tool_msg)
                
                # 使用通用的大内容处理逻辑
                try:
                    output_content = await MessageAnalyzer._process_large_content(tool_name, analysis.text)
                except Exception as e:
                    output_content = f"大内容处理失败: {str(e)}\n\n原始输出:\n{analysis.text}"
            
            return f"""
<工具调用结果>
<输入>
{input_str}
</输入>

<输出>
{output_content}
</输出>
</工具调用结果>

----
"""
        else:
            # 对于没有tool_call的情况，也检查工具名过滤
            tool_name = getattr(tool_msg, 'name', 'unknown')
            
            if tool_name in filtered_tools:
                result_content = "[输出已过滤]"
            else:
                analysis = MessageAnalyzer._analyze_single_tool(tool_msg)
                
                # 使用通用的大内容处理逻辑
                try:
                    result_content = await MessageAnalyzer._process_large_content(tool_name, analysis.text)
                except Exception as e:
                    result_content = f"大内容处理失败: {str(e)}\n\n原始输出:\n{analysis.text}"
            
            return f"Tool: {tool_name}\nResult: {result_content}"
    
    @staticmethod
    async def _process_large_content(tool_name: str, content: str) -> str:
        """
        处理大型工具输出内容的通用方法
        
        Args:
            tool_name: 工具名称
            content: 原始内容
        
        Returns:
            处理后的内容
        """
        # 使用通用的大内容管理器处理
        processed_content = await large_content_manager.process_large_content(tool_name, content)
        
        # 如果没有找到合适的处理器，返回原内容
        return processed_content if processed_content is not None else content


# ==================== 大内容处理 ====================

from .large_content_processor import large_content_manager


class PhaseDecider:
    """阶段决策器 - 简化决策逻辑"""
    
    @staticmethod
    async def decide_phase(messages: List[BaseMessage], 
                    same_tool_threshold: int = 3, 
                    any_tool_threshold: int = 5) -> Tuple[ExecutionPhase, Dict[str, Any]]:
        """决定执行阶段"""
        if not messages or not isinstance(messages[-1], ToolMessage):
            return ExecutionPhase.SELECT, {}
        
        # 分析工具执行结果
        analysis = await MessageAnalyzer.analyze_tool_message(messages)
        same_tool_failures, any_tool_failures = MessageAnalyzer.get_failure_counts(messages)
        
        # 决策逻辑
        if analysis.success:
            phase = ExecutionPhase.FINAL
            reason = "Tool execution successful"
        elif same_tool_failures >= same_tool_threshold:
            phase = ExecutionPhase.FINAL
            reason = f"Same tool failed {same_tool_failures} times"
        elif any_tool_failures >= any_tool_threshold:
            phase = ExecutionPhase.FINAL
            reason = f"Any tool failed {any_tool_failures} times"
        else:
            phase = ExecutionPhase.SELECT
            reason = "Continue tool selection"
        
        context = {
            "analysis": analysis,
            "same_tool_failures": same_tool_failures,
            "any_tool_failures": any_tool_failures,
            "reason": reason,
            "tool_result": analysis.text,
            "tool_result_empty": analysis.empty
        }
        
        return phase, context


# ==================== 主要接口 ====================

async def create_agent_with_tools(
    agent_type: str, 
    tools: List, 
    config: RunnableConfig,
    step_title: str = None,
    step_description: str = None,
    request_id: str = None
):
    """创建带工具的agent - 简化版本"""
    
    configurable = Configuration.from_runnable_config(config)
    mcp_tool_manager = MCPToolManager()
    sop_service = create_sop_service()
    
    async def generate_prompt(agent_state):
        """生成提示词"""
        messages = agent_state.get("messages", [])
        
        # 决定阶段
        phase, context = await PhaseDecider.decide_phase(messages)
        
        # 准备状态
        current_state = ReasoningState()
        current_state["request_id"] = request_id
        current_state["messages"] = []
        
        if phase == ExecutionPhase.SELECT:
            # 工具选择阶段
            prompt_name = f"agent_{agent_type}_select"
            
            # 获取SOP工具过滤
            sop_name = agent_state.get("sop_name", "")
            tool_names = None
            if sop_name:
                sop_obj = await sop_service.store.get_sop_by_id(sop_name)
                if sop_obj:
                    tool_names = sop_obj.tool_dependencies
            
            current_state["mcp_servers_description"] = await mcp_tool_manager.get_enabled_mcp_tools_description(tool_names)
        else:
            # 最终回答阶段
            prompt_name = f"agent_{agent_type}_final"
            current_state["tool_result"] = context.get("tool_result", "")
            current_state["tool_result_empty"] = context.get("tool_result_empty", True)
        
        # 注入步骤信息
        if step_title is not None:
            current_state["step_title"] = step_title
        if step_description is not None:
            current_state["step_description"] = step_description
        
        return apply_prompt_template(prompt_name, current_state, configurable)
    
    # 配置LLM
    llm = get_llm_by_type(AGENT_LLM_MAP[agent_type])
    if hasattr(llm, 'parallel_tool_calls'):
        llm.parallel_tool_calls = True
    elif hasattr(llm, 'bind'):
        llm = llm.bind(parallel_tool_calls=True)
    
    return create_react_agent(
        name=agent_type,
        model=llm,
        tools=tools,
        prompt=generate_prompt,
    )


async def setup_mcp_tools(agent_type: str, config: RunnableConfig) -> List:
    """设置MCP工具"""
    try:
        mcp_tool_manager = MCPToolManager()
        return await mcp_tool_manager.get_enabled_mcp_tools()
    except Exception:
        return []


def get_recursion_limit() -> int:
    """获取递归限制"""
    return DEFAULT_MAX_RECURSIVE_NUM


