import asyncio
import logging
from typing import Optional

from deep_diagnose.common.utils.string_utils import is_blank
from deep_diagnose.common.utils.task_scheduler import Task
from deep_diagnose.core.interactive.utils.dataset_collector import SLSDatasetCollector
from deep_diagnose.core.interactive.utils.tool_provider import ToolProvider

logger = logging.getLogger(__name__)

default_response = """您好，我是CloudBot诊断助手。
您可以通过自然语言与我进行交互，实现通用ECS知识咨询，以及资源（VM或NC）的信息查询、问题诊断、运维提交等。
请您明确输入目标资源、时间和诉求，尽量减少语言歧义。
我可以根据您的提问，自主规划工具调用，并汇总各工具的输出以回复您的提问。典型的提问如下：

+ 热迁移错误码xxx的含义是什么（知识库查询）
+ i-xxxx和i-yyyy的规格是什么（基本信息查询）
+ 物理机aa.bb.cc.dd在今天10点左右为什么会发生宕机（宕机诊断）
+ 热迁移i-xxxx（运维提交）

您可以阅读[使用指南](https://alidocs.dingtalk.com/i/nodes/m9bN7RYPWdyrPBREcoL3B5jkVZd1wyK0)来进一步了解我，共同实现AI赋能值班提效。"""

class ExecutionState:
    def __init__(self):
        # 控制选项
        self.history_rounds = 10
        self.slow_path_enable = True
        self.fast_path_enable = True
        self.fast_path_mode = 'default'
        self.param_extract_with_regex = True
        # 通用
        self.raw_question = ''
        self.chat_history = None
        self.tool_provider : Optional[ToolProvider] = None
        self.dataset_collector = SLSDatasetCollector()
        self.finished = False
        # 改写
        self.question = ''
        self.rewrite_finished = False
        # 慢速路径规划
        self.plan_content = ''
        self.analyzed_chars_of_plan = 0
        self.analyzed_steps = set()
        self.task_queue = asyncio.Queue[Optional[Task]]()
        self.plan_finished = False
        # 快速路径规划
        self.fast_path_match_completed = False
        self.fast_path_scenario = None
        # 执行
        self.step_states = dict[str, StepState]()
        # 总结
        self.conclusion_started = False
        self.raw_result_content = ''
        self.reason_content = ''
        self.result_content = ''

    def is_finished(self) -> bool:
        return self.finished

    def to_display_str(self) -> str:
        if self.fast_path_mode == "instruction":
            return self._to_display_str_instruction()
        else:
            return self._to_display_str_default()

    def _to_display_str_default(self) -> str:
        """
        将 ExecutionState 对象转换为字符串格式，用于展示诊断执行的步骤信息、思考内容和结果回复。
        
        返回:
            str: 包含所有步骤状态信息、思考内容和结果回复的字符串表示。
            
        说明:
            - 如果没有步骤状态，则返回“正在生成解决方案”。
            - 按照 step_id 排序后输出每个 StepState 的展示字符串。
            - 如果存在 reason_content，则追加“### 思考”部分。
            - 如果存在 result_content，则追加“### 回复”部分。
        """
        contents = []
        try:
            if not self.rewrite_finished:
                return "正在基于上下文改写问题"
            if not self.conclusion_started:
                if not self.fast_path_match_completed:
                    return "正在尝试匹配快速路径"
                elif self.fast_path_scenario is None and len(self.step_states) == 0:
                    return "未命中快速路径，正在生成解决方案"

            # 添加问题改写结果
            contents.append(f'<details>\n<summary>Rewrite Question</summary>')
            contents.append(self.question)
            contents.append('</details>')
            # 遍历按 step_id 排序后的 step_states，生成每个步骤的展示字符串
            for step_id, step_state in sorted(self.step_states.items()):
                contents.append(step_state.to_display_str())
            # 添加思考内容（如果存在）
            if not is_blank(self.reason_content):
                contents.append("## 思考")
                contents.append(self.reason_content)
            # 添加回复内容（如果存在）
            if not is_blank(self.result_content):
                contents.append("## 回复")
                contents.append(self.result_content)
        except Exception as e:
            logger.error(f"Generate state display content error: {e}", exc_info=True)

        # 使用换行符连接所有内容并返回
        return str.join("\n\n", contents)

    def _to_display_str_instruction(self) -> str:
        return self.result_content if not is_blank(self.result_content) else ""


class StepState:

    def __init__(self, step: dict):
        self.tool = step.get('tool', None)
        self.id = step.get('id', None)
        self.input = step.get('input', None)
        self.analysis = step.get('analysis', None)
        self.tool_states = []
        self.status = "Wait"

    def to_display_str(self) -> str:
        """
        将当前步骤状态转换为HTML格式的字符串表示，用于展示。
        
        返回:
            str: 包含步骤信息的HTML格式字符串，包括ID、工具名称、状态、输入和输出。
        """
        # 设置对应的工具名称
        tool = "Thinking" if is_blank(self.tool) else self.tool
        # 生成HTML内容
        contents = []
        if len(self.tool_states) == 0:
            contents.append(f'<details>\n<summary>({self.id}) {tool} [{self.status}]</summary>\n</details>')
        for i in range(len(self.tool_states)):
            item_id = f"{self.id}-{i + 1}" if len(self.tool_states) > 1 else f"{self.id}"
            contents.append(f'<details>\n<summary>({item_id}) {tool} [{self.tool_states[i].status}]</summary>')
            if self.tool_states[i].input:
                contents.append('### Input')
                contents.append(f'{self.tool_states[i].input}')
            if self.tool_states[i].result:
                contents.append('### Output')
                contents.append(f'{self.tool_states[i].result}')
            if self.tool_states[i].error:
                contents.append('### Error')
                contents.append(f'{self.tool_states[i].error}')
            contents.append('</details>')
        return str.join("\n\n", contents)


class ToolState:
    def __init__(self, input_params):
        self.status = "Executing"
        self.input = input_params
        self.result = None
        self.error = None


class AoneState:

    def __init__(self):
        self.aone_content = ""
        self.summary = ""
        self.solution = ""
        self.duty = ""
        self.finished = False
