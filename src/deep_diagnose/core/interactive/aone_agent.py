import asyncio
import logging
from typing import Optional, Dict, Any, List, AsyncIterator

from deep_diagnose.core.agent import BaseGraph
from deep_diagnose.core.events.aone_event import AoneEvent
from deep_diagnose.core.events.base_event import BaseAgentOutputEvent
from deep_diagnose.core.interactive.interactive_agent import InteractiveAgent
from deep_diagnose.core.interactive.state import AoneState
from deep_diagnose.llms.app_bailian import app_bailian, BailianAppEnum

logger = logging.getLogger(__name__)


class AoneAgent(BaseGraph):

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        # 调用父类的初始化方法，传递配置参数
        super().__init__(config)

    async def astream(self, question: str, messages: Optional[List[Dict[str, Any]]] = None, **kwargs) \
            -> AsyncIterator[BaseAgentOutputEvent]:
        # 创建State
        state = AoneState()
        state.aone_content = question

        # 启动主流程
        main_task = asyncio.create_task(self._main_flow(state))

        # 监控执行状态并生成输出
        try:
            async for event in self._state_monitor(state):
                yield event
        except Exception as e:
            logger.error(f"Aone agent task failed: {e}", exc_info=True)
            main_task.cancel()
        pass

    @staticmethod
    async def _state_monitor(state: AoneState):
        """
        异步监控Aone状态变化的生成器函数
        
        该函数会持续检查state的完成状态，直到状态标记为完成为止。在每次检查之间会等待0.5秒，
        以避免过于频繁的轮询。当状态完成时，会最后一次返回最终状态。
        
        Args:
            state (AoneState): 需要监控的Aone状态对象
            
        Yields:
            AoneState: 当前的Aone状态对象
        """
        # 循环检查状态是否完成
        while not state.finished:
            # 返回当前状态
            event = AoneEvent()
            event.parse_graph_event(state)
            yield event
            # 等待0.5秒后继续检查
            await asyncio.sleep(0.5)
        # 状态已完成，返回最终状态
        event = AoneEvent()
        event.parse_graph_event(state)
        yield event

    async def _main_flow(self, state: AoneState):
        # 总结Aone
        await self._summary(state)
        # 生成初步诊断结果
        await asyncio.gather(self._duty_route(state), self._diagnose(state))
        # 设置结束标记
        state.finished = True

    @staticmethod
    async def _summary(state: AoneState):
        """
        异步流式调用百炼应用对Aone内容进行摘要总结，直接修改state.summary属性
        
        Args:
            state (AoneState): 包含aone_content和summary状态的对象
            
        Returns:
            None
        """
        # 调用百炼应用流式接口进行摘要生成
        responses = app_bailian.app_stream_call(app=BailianAppEnum.aone_summarizer, prompt=state.aone_content)
        # 异步迭代处理流式响应结果
        async for result, _ in responses:
            if result:
                # 将流式返回的摘要内容累加到state.summary中
                state.summary += result

    @staticmethod
    async def _duty_route(state: AoneState):
        question = f"FASTPATH_DUTY@BAILIAN,{state.summary}"
        agent = InteractiveAgent()
        async for value in agent.astream(question, mode="instruction"):
            state.duty = value.result

    @staticmethod
    async def _diagnose(state: AoneState):
        question = f'''
        下面是客户的一个问题。请充分调用相关的MCP工具(包括知识库)，综合各个工具的与问题相关的返回结果（不要罗列无关信息），
        尝试解决下面的问题，给出初步的诊断结论和建议。注意，输出应当尽量精简，不要包含markdown的标题、列表、表格等。
        
        {state.summary}
        '''
        agent = InteractiveAgent()
        async for value in agent.astream(question, mode="deep"):
            state.solution = value.result


async def _main() -> None:
    agent = AoneAgent()
    question = '''
    2025-08-21 03:28:36
i-uf6h2sumugq6tp2uay8r
实例规格:ecs.g6.large
cn-shanghai-g
包年包月
这个实例在检测出异常后，为何没有自动进行热迁移
记得之前有和稳定性团队沟通过，这块的策略是在发送主动运维事件前，后台会主动热迁移一次，失败后才会发主动运维事件,这次后台热迁移失败的原因是什么，客户对于这种事件比较敏感，看我们这边有没有什么措施，可以提高稳定性
    '''

    async for value in agent.astream(question):
        print(value)


if __name__ == "__main__":
    # 运行主程序
    asyncio.run(_main())
