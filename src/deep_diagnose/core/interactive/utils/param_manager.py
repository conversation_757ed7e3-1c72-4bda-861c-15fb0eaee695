import logging
import re
import datetime
from typing import Optional
from langchain_core.tools.base import BaseTool
from deep_diagnose.common.utils.string_utils import contains_keywords, is_blank

logger = logging.getLogger(__name__)


class ContextualParamManager:
    """
    ContextualParamManager类用于管理参数，基于上下文进行参数验证、参数提取等操作。
    """

    # 各种参数需要符合的规则（正则表达式）
    resource_pattern_map = {
        "uid": r'\b\d{5,8}|\d{16}\b(?!-)',
        "eventId": r'\be-[a-z0-9-]+\b(?!-)',
        "diskId": r'\bd-[a-z0-9-]+\b(?!-)',
        "instanceId": r'\b(?:i-|hbm-|AY|eci-|cp-|ic-|ay|acs-)[a-z0-9]+\b(?!-)',
        "ncIp": r'\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b(?!-)',
        "ncId": r'\b(?:\d{4,5}-\d{1,5})\b(?!-)'
    }
    resource_pattern_map["nc"] = resource_pattern_map['ncId'] + "|" + resource_pattern_map['ncIp']
    resource_pattern_map["machineId"] = resource_pattern_map['nc'] + "|" + resource_pattern_map['instanceId']

    time_pattern = r'\b\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\b'
    markdown_link_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
    url_pattern = r'\bhttps?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[-\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:[\w.])*)?)?\b'

    def __init__(self):
        pass

    def validate_params(self, params: dict, context: str) -> str | None:
        """
        验证参数中的关键字段值是否存在于上下文中。

        该函数主要用于确保参数中与虚拟机（VM）、宿主机（NC）、实例或机器相关的字段值
        在给定的上下文中存在，以防止无效的参数传入工具调用。

        参数:
            params (dict): 需要验证的参数字典。
            context (str): 上下文字符串，通常包含有效的字段值（如VM ID、NC IP等）。

        返回:
            Optional[str]: 如果发现无效字段值，返回错误信息；否则返回 None。
        """

        # 确保参数为字典类型
        if not isinstance(params, dict):
            return "Generated param is not dict"

        # 定义需验证的关键字集合
        keywords = list(self.resource_pattern_map.keys())

        # 遍历参数字典中的每一个键值对
        for key in params:
            # 如果键名不含关键字，则跳过
            contained_keyword = contains_keywords(key, keywords)
            if not contained_keyword:
                continue
            param_value = params[key]
            # 统一转为列表处理
            values = [param_value] if isinstance(param_value, str) else param_value
            # 具体执行校验
            for item in values:
                error = self._validate_param_value(contained_keyword, item, context)
                if error is not None:
                    return error
        # 所有参数验证通过
        return None

    def _validate_param_value(self, param_key: str, param_value: str, context: str) -> str | None:
        if param_value not in context:
            return f"{param_value} is not found in context"
        if re.fullmatch(self.resource_pattern_map[param_key], param_value, flags=re.ASCII) is None:
            return f"{param_value} does not match regex pattern"
        return None

    def sanitize_content(self, content: str, context: str) -> str:
        """
        从content中找到所有的URL，检查其是否在context中曾经出现过。
        如果不曾出现过，则将其加上删除线（前后都加上~~）。
        如果URL是Markdown格式的超链接，则将其整个加删除线。

        参数:
            content (str): 原始内容
            context (str): 上下文字符串，用于验证URL是否合法

        返回:
            str: 处理后的内容
        """

        # 处理普通URL
        def process_plain_url(match):
            url = match.group(0)

            # 检查URL是否在上下文中出现过
            if url not in context:
                return f" ~~{url}~~ "
            return url

        # 处理普通URL，使用负向前瞻避免匹配Markdown链接中的URL
        content = re.sub(self.url_pattern, process_plain_url, content, flags=re.ASCII)

        # 再处理Markdown格式的链接，注意链接中的URL可能已经被加了删除线，需要去除
        def process_markdown_link(match):
            full_match = match.group(0)
            link_name = match.group(1).strip("~ ")
            url = match.group(2).strip("~ ")

            # 检查URL是否在上下文中出现过
            if url not in context:
                return f" ~~[{link_name}]({url})~~ "
            return full_match

        # 处理Markdown链接
        content = re.sub(self.markdown_link_pattern, process_markdown_link, content, flags=re.ASCII)

        return content

    def extract_param(self, context: str, tool: BaseTool) -> Optional[dict]:
        """
        从上下文中提取工具所需的参数值。

        参数:
            context (str): 上下文字符串，用于提取参数值
            tool (BaseTool): 工具对象，包含所需的参数信息

        返回:
            dict | None: 提取成功时返回参数字典，失败时返回None
        """
        result = {}

        # 获取工具的所有参数名
        tool_param_names = tool.args.keys()

        # 构造关键词列表
        keywords = list(self.resource_pattern_map.keys()) + ["startTime", "endTime"]

        # 检查所有参数是否都包含关键词
        for param_name in tool_param_names:
            contained_keyword = contains_keywords(param_name, keywords)
            if not contained_keyword:
                return None

        # 为每个参数提取值
        for param_name in tool_param_names:
            contained_keyword = contains_keywords(param_name, keywords)

            if contained_keyword in ["startTime", "endTime"]:
                # 时间参数提取
                matches = list(set(re.findall(self.time_pattern, context, flags=re.ASCII)))
                if len(matches) == 1:
                    result[param_name] = matches[0]
                elif len(matches) == 2:
                    if contained_keyword == "startTime":
                        result[param_name] = min(matches[0], matches[1])
                    else:
                        result[param_name] = max(matches[0], matches[1])
                else:
                    return None
            else:
                # 资源参数提取
                pattern = self.resource_pattern_map[contained_keyword]
                matches = list(set(re.findall(pattern, context, flags=re.ASCII)))
                if len(matches) != 1:
                    return None
                param_type = tool.args[param_name].get('type')
                if param_type == 'array':
                    result[param_name] = [matches[0]]
                elif param_type == 'string':
                    result[param_name] = matches[0]
                else:
                    return None

        return result

    def extract_resources(self, context: str) -> list[str]:
        """
        从给定的上下文中提取所有匹配machineId模式的资源标识符。

        参数:
            context (str): 需要搜索的上下文字符串

        返回:
            list[str]: 匹配到的所有唯一machineId列表
        """
        matches = re.findall(self.resource_pattern_map['machineId'], context, flags=re.ASCII)
        return list(set(matches))

    @staticmethod
    def adjust_time_range_in_params(time_dict: dict) -> dict:
        """
        检查并调整时间范围，确保 endTime 比 startTime 晚至少2小时。

        参数:
            time_dict (dict): 包含 startTime 和 endTime 字段的字典，格式为 yyyy-MM-dd HH:mm:ss

        返回:
            dict: 调整后的时间字典
        """

        # 获取时间字段
        start_time_str = time_dict.get('startTime', '') or time_dict.get('start_time', '')
        end_time_str = time_dict.get('endTime', '') or time_dict.get('end_time', '')

        if is_blank(start_time_str) or is_blank(end_time_str):
            # 如果不存在时间字段，则直接返回原始字典
            return time_dict

        try:
            # 解析时间字符串
            start_time = datetime.datetime.strptime(start_time_str, '%Y-%m-%d %H:%M:%S')
            end_time = datetime.datetime.strptime(end_time_str, '%Y-%m-%d %H:%M:%S')
        except ValueError as e:
            logger.warning(f"Invalid time format in adjust_time_range: {e}")
            return time_dict

        # 如果 endTime 小于等于 startTime，则交换两个时间
        if end_time <= start_time:
            start_time, end_time = end_time, start_time

        # 确保 endTime 比 startTime 晚至少2小时，否则前后各扩展1小时
        if end_time < start_time + datetime.timedelta(hours=2):
            start_time = start_time + datetime.timedelta(hours=-1)
            end_time = end_time + datetime.timedelta(hours=1)

        # 更新字典中的时间值，支持两种格式
        if "startTime" in time_dict and "endTime" in time_dict:
            time_dict['startTime'] = start_time.strftime('%Y-%m-%d %H:%M:%S')
            time_dict['endTime'] = end_time.strftime('%Y-%m-%d %H:%M:%S')
        if "start_time" in time_dict and "end_time" in time_dict:
            time_dict['start_time'] = start_time.strftime('%Y-%m-%d %H:%M:%S')
            time_dict['end_time'] = end_time.strftime('%Y-%m-%d %H:%M:%S')

        return time_dict

    def validate_resource_pattern(self, resource_type: str, value: str) -> bool:
        """
        验证字符串是否符合指定的资源类型规则

        参数:
            resource_type (str): 资源类型，如 'uid', 'eventId', 'instanceId' 等
            value (str): 需要验证的字符串值

        返回:
            bool: 符合规则返回True，否则返回False
        """

        # 检查资源类型是否存在
        if resource_type not in self.resource_pattern_map:
            raise ValueError(f"不支持的资源类型: {resource_type}")

        # 使用完整匹配验证字符串是否符合规则
        pattern = self.resource_pattern_map[resource_type]
        return re.fullmatch(pattern, value, flags=re.ASCII) is not None
