import asyncio
import json
import logging
import uuid

from aliyun.log import LogClient, LogItem, PutLogsRequest

from deep_diagnose.common.config import get_config


# 共享的SLS Client
def _init_client() -> LogClient:
    ak = get_config().infrastructure.sls.access_key_id
    sk = get_config().infrastructure.sls.access_key_secret
    endpoint = get_config().infrastructure.sls.endpoint
    return LogClient(endpoint, ak, sk)


_client = _init_client()
logger = logging.getLogger(__name__)


class SLSDatasetCollector:
    def __init__(self, client=_client):
        self.client = client
        self.project = "ecs-predict-result"
        self.logstore = "ecs_deep_diagnose_interactive_dataset"
        self.log_queue = []
        self.uuid = str(uuid.uuid4())

    def add_log(self, record: dict):
        """
        将日志记录添加到队列中，不阻塞主流程
        """
        try:
            # 转换 record 为日志项
            contents = []
            for k, v in record.items():
                # 所有值转为字符串
                contents.append((str(k), json.dumps(v, ensure_ascii=False) if isinstance(v, (dict, list)) else str(v)))
            contents.append(("uuid", self.uuid))
            log_item = LogItem()
            log_item.set_contents(contents)

            # 添加到队列中
            self.log_queue.append(log_item)
        except Exception as e:
            # 注意：不要让日志上报失败影响主流程
            logger.error(f"Failed to queue record {record}: {e}")

    async def batch_put_logs(self):
        """
        批量发送日志到SLS
        
        该方法将当前日志队列中的所有日志项通过SLS客户端发送到指定的project和logstore中。
        使用asyncio.to_thread来在单独的线程中执行阻塞的put_logs操作，避免阻塞事件循环。
        整个操作设置10秒超时限制。
        
        Returns:
            None
        """
        if not self.log_queue:
            return

        try:
            # 构造批量日志发送请求
            request = PutLogsRequest(
                project=self.project,
                logstore=self.logstore,
                logitems=self.log_queue
            )
            # 在独立线程中执行日志发送操作，并设置超时时间
            await asyncio.wait_for(
                asyncio.to_thread(self.client.put_logs, request),
                timeout=10.0
            )
        except Exception as e:
            # 捕获所有异常并打印错误信息，避免中断主流程
            logger.error(f"[SLS] Failed to send batch records: {e}")
