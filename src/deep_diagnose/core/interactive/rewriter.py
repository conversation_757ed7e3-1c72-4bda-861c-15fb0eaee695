import json
import logging
import time

from deep_diagnose.common.utils.json_utils import to_one_line_json
from deep_diagnose.common.utils.string_utils import substring_after
from deep_diagnose.core.interactive.state import ExecutionState, default_response
from deep_diagnose.core.interactive.utils.param_manager import ContextualParamManager
from deep_diagnose.llms.app_bailian import app_bailian, BailianAppEnum

logger = logging.getLogger(__name__)


async def rewrite_question(state: ExecutionState):
    """
    根据对话历史重写问题

    参数:
        state (ExecutionState): 执行状态对象，包含对话历史和当前问题
    """
    # 兜底，不改写
    state.question = state.raw_question

    # 计算需要使用的历史对话轮次
    rounds = min(state.history_rounds, 0 if state.chat_history is None else len(state.chat_history))
    if rounds > 0:
        # 如果有对话历史，则调用百炼rewriter进行改写
        try:
            # 生成对话历史
            history = _gen_history(state.chat_history[-rounds:])
            # 调用百炼应用进行重写
            time_before = time.time()
            rewritten_question = await app_bailian.app_call(
                app=BailianAppEnum.rewriter,
                prompt=state.raw_question,
                biz_params={"history": history}
            )
            state.question = rewritten_question
            time_after = time.time()
            # 记录模型调用日志
            log_dict = {
                "app_name": "rewriter",
                "prompt": state.raw_question,
                "biz_params": {"history": history},
                "result": rewritten_question,
                "time_cost": time_after - time_before
            }
            state.dataset_collector.add_log(log_dict)
        except Exception as e:
            logger.error(f"Failed to rewrite question: {e}", exc_info=True)

    # 标记改写完成
    state.rewrite_finished = True


def _gen_history(messages) -> str:
    """
    根据对话消息列表生成历史记录字符串
    
    该函数处理不同类型的agent消息:
    - InteractiveAgent: 处理诊断助手的回复，提取纯文本内容
    - InspectAgent: 从扩展信息中提取机器ID、时间等信息，构造成完整诉求
    
    Args:
        messages: 包含对话消息的列表，每条消息包含agent、role、content等字段
        
    Returns:
        str: 格式化的历史记录字符串，每行包含角色和内容，以换行符分隔
    """
    contents = []
    for message in messages:
        agent = message.get("agent", "")
        role = message.get("role", "")
        content = message.get("content", "")
        if agent == "InteractiveAgent":
            if role == "assistant":
                # 诊断助手的回复忽略前面的工具调用结果等
                content = json.loads(content)
                content = substring_after(content.get('result', ""), "## 回复\n\n")
                if content == default_response:
                    content = "问题无法识别"
        elif agent == "InspectAgent":
            if role == "user":
                # 从ext中提取信息，拼成完整的诉求
                ext = message.get('ext', {})
                machine_id = ext.get("machine_id", "")
                start_time = ext.get("start_time", "")
                end_time = ext.get("end_time", "")
                manager = ContextualParamManager()
                if manager.validate_resource_pattern("instanceId", machine_id):
                    machine_type = "虚拟机"
                elif manager.validate_resource_pattern("nc", machine_id):
                    machine_type = "物理机"
                else:
                    machine_type = ""
                content = f"[{start_time} - {end_time}] 对{machine_type}{machine_id}进行检查和诊断。"
        else:
            # 忽略其他Agent的消息
            continue
        # 去除换行符
        content = content.replace("\n", "")
        contents.append(f"{role}: {content}")
    return "\n".join(contents)
