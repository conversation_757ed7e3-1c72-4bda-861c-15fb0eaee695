#!/bin/bash
# iFlow MCP服务器配置命令行脚本

# FVT服务器
iflow mcp add --transport http fvt http://pre-xmca-cloudbot.aliyun-inc.com/fvt/mcp/ \
  --header "Content-Type: application/json" \
  --header "Authorization: Bearer M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU="

# 基础信息服务器
iflow mcp add --transport http basic_info http://xmca-cloudbot.aliyun-inc.com/basic_info/mcp/ \
  --header "Content-Type: application/json" \
  --header "Authorization: Bearer M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU="

# NC下线生产服务器
iflow mcp add --transport http nc_down_prod http://xmca-cloudbot.aliyun-inc.com/nc_down/mcp/ \
  --header "Content-Type: application/json" \
  --header "Authorization: Bearer M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU="

# NC下线预发服务器
iflow mcp add --transport http nc_down_pre http://pre-xmca-cloudbot.aliyun-inc.com/nc_down/mcp/ \
  --header "Content-Type: application/json" \
  --header "Authorization: Bearer M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU="

# 运维操作服务器
iflow mcp add --transport http operation http://xmca-cloudbot.aliyun-inc.com/operation/mcp/ \
  --header "Content-Type: application/json" \
  --header "Authorization: Bearer M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU="

# 诊断服务器
iflow mcp add --transport http diagnose http://xmca-cloudbot.aliyun-inc.com/diagnose/mcp/ \
  --header "Content-Type: application/json" \
  --header "Authorization: Bearer M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU="

# VM核心转储服务器 (使用SSE协议)
iflow mcp add --transport sse vm_coredump http://ecs-mcp.alibaba-inc.com/vm_coredump/mcp/ \
  --header "Authorization: token ************************************************"

# VM迁移服务器 (使用SSE协议)
iflow mcp add --transport sse vm_migration https://ecs-mcp.alibaba-inc.com/vm_migration/mcp/ \
  --header "Authorization: token ****************************************************"

echo "所有MCP服务器配置命令已生成完成"